{"name": "parsec-admin", "version": "2.4.34-alpha.1", "license": "ISC", "scripts": {"start": "yarn compile && dumi dev", "docs:build": "yarn compile && dumi build", "docs:deploy": "gh-pages -d docs-dist", "compile": "father-build", "dev": "father-build -w", "deploy": "npm run docs:build && npm run docs:deploy", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "module": "./es/index", "main": "./lib/index", "typings": "./lib/index.d.ts", "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.0", "@ant-design/icons": "^4.6.2", "@babel/runtime": "^7.10.4", "@types/file-saver": "^2.0.1", "ahooks": "^2.10.9", "apollo-link-error": "^1.1.13", "apollo-link-http": "^1.5.17", "babel-plugin-inline-import-data-uri": "^1.0.1", "bignumber.js": "^9.3.0", "circular-json": "^0.5.9", "classnames": "^2.2.6", "fabric": "3.6.6", "file-saver": "^2.0.2", "format-json": "^1.0.3", "graphql": "^15.4.0", "html2canvas": "^1.3.2", "jr-qrcode": "^1.1.4", "js-export-excel": "^1.1.3", "parsec-hooks": "^1.0.18", "paste-from-word": "^1.0.5", "qiankun": "^2.5.1", "qqmap": "^1.0.1", "react-keepalive-router": "^1.1.3", "react-keydown": "^1.9.12", "react-markdown": "^7.0.1", "react-sortable-hoc": "^2.0.0", "wangeditor": "^4.7.9", "xss": "^1.0.14"}, "peerDependencies": {"antd": "^4.x", "axios": "^0.x", "moment": "^2.x", "react": "*", "react-dom": "*", "react-router-dom": "^5.x", "styled-components": "^5.x"}, "devDependencies": {"@simbathesailor/babel-plugin-use-what-changed": "^0.1.17", "@simbathesailor/use-what-changed": "^0.1.25", "@types/classnames": "^2.2.10", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@types/styled-components": "^5.1.0", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "@umijs/preset-react": "1.x", "@umijs/test": "^3.0.5", "antd": "^4.22.1", "axios": "0.19.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "dumi": "^1.1.26", "eslint": "^7.13.0", "eslint-config-prettier": "^6.15.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-unicorn": "^23.0.0", "father-build": "^1.17.2", "gh-pages": "^3.0.0", "lint-staged": "^10.0.7", "moment": "^2.29.1", "np": "^6.3.0", "prettier": "^1.19.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.3.0", "styled-components": "^5.3.1", "typescript": "^4.7.4", "yorkie": "^2.0.0"}, "resolutions": {"typescript": "^4.7.4", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6"}, "files": ["lib", "es"]}