stages:
  - build
  - alpha
  - staging
  - production
  - publish

variables:
  BUILD_IMAGE: 'node:14'
  GROUP_NAME: doc
  APP_NAME: admin
  PREPARE_CMD: yarn #安装依赖包的命令
  DIST_PATH:
    docs-dist #打包结果的路径
    # 取消git clean删除node_modules的操作  https://docs.gitlab.com/ee/ci/large_repositories/#store-custom-clone-options-in-configtoml
  GIT_CLEAN_FLAGS: -ffdx -e node_modules/ -e .yarn

##------------- production阶段 -------------
build_app:
  stage: build
  image: $BUILD_IMAGE
  variables:
    CI: 'true'
  script:
    - yarn
    - yarn docs:build
  artifacts:
    paths:
      - $DIST_PATH/
    expire_in: 10 hours
  tags:
    - kq-front-test
  only:
    - master

app:
  stage: production
  script:
    - pwd
    - rm -rf _dist.d && cp -r $DIST_PATH _dist.d
    - docker-compose -p $CI_JOB_NAME-$GROUP_NAME-$APP_NAME -f docker/docker-compose.yml up -d --build
  environment:
    name: $CI_JOB_NAME
    url: http://parsec-admin.parsec.com.cn
  dependencies:
    - build_app
  only:
    - master
  tags:
    - staging #runner名称

publish:
  stage: publish
  image: $BUILD_IMAGE
  variables:
    CI: 'true'
    NPM_TOKEN: $NPM_TOKEN
  interruptible: true
  script:
    - echo "registry=https://registry.npmjs.org/" > .npmrc
    - echo "//registry.npmjs.org/:always-auth=true" >> .npmrc
    - echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" >> .npmrc
    - cat .npmrc
    - yarn
    - yarn compile
    - npm publish
  tags:
    - kq-front-test
  only:
    - /^v.*$/
