import { defineConfig } from 'dumi';

export default defineConfig({
  title: 'Parsec Admin',
  favicon: 'https://gw.alipayobjects.com/zos/rmsportal/rlpTLlbMzTNYuZGGCVYM.png',
  logo: 'https://gw.alipayobjects.com/zos/rmsportal/rlpTLlbMzTNYuZGGCVYM.png',
  outputPath: 'docs-dist',
  publicPath: '/docs/kaiqiao-admin/',
  base: '/docs/kaiqiao-admin/',
  mode: 'site',
  extraBabelPlugins: [
    ['babel-plugin-import', {
      libraryName: 'antd',
      libraryDirectory: 'es',
      style: true,
    }],
    [
      "@simbathesailor/babel-plugin-use-what-changed",
      {
        "active": process.env.NODE_ENV === "development" // boolean
      }
    ]
  ]
  // more config: https://d.umijs.org/config
});
