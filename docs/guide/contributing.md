# 贡献指南

> 全片文章修改自 [Antd 贡献指南](https://ant.design/docs/react/contributing-cn)

这篇指南会指导你如何为 Parsec Admin 贡献一份自己的力量，请在你要提 issue 或者 merge request 之前花几分钟来阅读一遍这篇指南。

## 行为准则

推荐查看 [Antd 行为准则](https://github.com/ant-design/ant-design/blob/master/CODE_OF_CONDUCT.md)，希望所有的贡献者都能遵守，请花时间阅读一遍全文以确保你能明白哪些是可以做的，哪些是不可以做的。

## 透明的开发

我们所有的工作都会放在 [GitLab](https://gitlab.parsec.com.cn/web1/parsec-admin) 上。不管是核心团队的成员还是外部贡献者的 merge request 都需要经过同样流程的 review。

## 分支管理

基于我们的 [发布周期](/guide/changelog)，我们长期维护两个分支 `master` 和 `feature`。如果你要修一个 bug，那么请发 merge request 到 `master`；如果你要提一个增加新功能的 merge request，那么请基于 `feature` 分支来做。

## Bugs

在你报告一个 bug 之前，请先确保已经搜索过已有的 issue 和阅读了我们的 [常见问题](/guide/faq)。

## 新增功能

如果你有改进我们的 API 或者新增功能的想法，我们同样推荐你使用 [GitLab](https://gitlab.parsec.com.cn/web1/parsec-admin/issues/new) 来新建一个添加新功能的 issue。

如果你希望协助开发新的 API，推荐参考 [Antd API 规范](https://github.com/ant-design/ant-design/wiki/API-Naming-rules) 进行命名。

## 第一次贡献

如果你还不清楚怎么在 GitLab 上提 Merge Request ，可以阅读下面这篇 GitHub 贡献指南文章来学习：

[如何优雅地在 GitHub 上贡献代码](https://segmentfault.com/a/1190000000736629)

如果你打算开始处理一个 issue，请先检查一下 issue 下面的留言以确保没有别人正在处理这个 issue。如果当前没有人在处理的话你可以留言告知其他人你将会处理这个 issue，以免别人重复劳动。

如果之前有人留言说会处理这个 issue 但是一两个星期都没有动静，那么你也可以接手处理这个 issue，当然还是需要留言告知其他人。

## Merge Request

[MuShan](https://gitlab.parsec.com.cn/mushan) 会负责关注所有的 merge request，他会 review 以及合并你的代码，也有可能要求你做一些修改或者告诉你我们为什么不能接受这样的修改。

**在你发送 Merge Request 之前**，请确认你是按照下面的步骤来做的：

1. 基于 [正确的分支](#分支管理) 做修改。
2. 在项目根目录下运行了 `yarn`。
3. 运行 `yarn start` 会编译代码并监听你所做的更改。
4. 运行 `yarn docs` 会启动文档，这可以实时运行和预览你所做的更改。
5. 如果你修复了一个 bug 或者新增了一个功能，请确保写了相应的文档，这很重要。
6. 确保你的代码通过了 lint， 执行 `yarn lint-staged` 会简单地修复你的代码，或提示你的代码风格问题。

## 开发流程

在你 clone 了 parse admin 的代码并且使用 `yarn` 安装完依赖后，你还可以运行下面几个常用的命令：

1. `yarn start` 在本地编译 Parsec admin 代码。
2. `yarn docs` 运行文档网站。
3. `yarn build` 构建 parsec admin 到 dist 目录。
