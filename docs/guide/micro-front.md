# 微前端配置

修改`src/index`文件：

```diff
+ export const mount = props => registerMount(props, <App />);
+ export { unmount, update, bootstrap } from 'parsec-admin/lib/microAppConfig';

+ render(<App />);

- ReactDOM.render(<App />, document.getElementById('main-app'));

```

在主应用的`routes`配置里设置：

```tsx
// routes
[{
  path: '/app',
  name: '微应用',
  microAppName: '微应用名称', // 在微应用webpack设置的名字
  microAppUrl: '//localhost:3001' // 微应用的地址url，生产地址要换成发布后的地址
}]
```
