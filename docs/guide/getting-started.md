---
title: 快速上手
order: 0
nav:
  title: 文档
  order: 0
---

# 快速上手

使用 `create-parsec-app` 脚手架创建的你的应用。

> 脚手架还可以创建其他类型的项目

## 创建项目

```bash
$ yarn create parsec-app my-app
```

## 运行项目

```bash
$ cd my-app && yarn start
```

## 项目结构

现在我们来看一下 Parsec-App 应用的结构：

```bash
my-app/
┳ package.json
┣ node_modules/
┣ public/
┗━┓
  ┣ index.html
┣ src/
┗━┓ App.tsx
  ┣ configs/
  ┗━┓
    ┣ routes.tsx
    ┣ apis.tsx
  ┣ pages/
  ┗━┓
    ┣ home/
    ┗━┓
      ┣ index.tsx
```

`public/index.html` 为整个项目的索引 html ，可以配置页面 title/favicon 等。

`src/` 为源文件目录。

`App.tsx` 入口文件。

`routes.tsx` 路由配置文件。

`api.tsx` 接口配置文件。

`pages/` 页面存放目录。
