---
title: Parsec Admin - 基于 Antd 快速搭建中后台应用
order: 10
hero:
  title: Parsec Admin
  desc: 基于 Antd 快速搭建中后台应用
  actions:
    - text: 快速上手
      link: /guide/getting-started
features:
  - icon: https://gw.alipayobjects.com/zos/bmw-prod/881dc458-f20b-407b-947a-95104b5ec82b/k79dm8ih_w144_h144.png
    title: 开箱即用
    desc: 配合 create-parsec-app 快速生成一个简单的应用
  - icon: https://gw.alipayobjects.com/zos/bmw-prod/d60657df-0822-4631-9d7c-e7a869c2f21c/k79dmz3q_w126_h126.png
    title: 易维护，对开发者友好
    desc: 使用 npm 包的方式维护一般开发中常用的组件、工具等
  - icon: https://gw.alipayobjects.com/zos/bmw-prod/d1ee0c6f-5aed-4a45-a507-339a4bfe076c/k7bjsocq_w144_h144.png
    title: 好看又好用
    desc: 以 Ant Design Pro 为模版基准开发组件，完全遵循 Ant Design 一整套设计语言
footer: Open-source MIT Licensed | Copyright © 2019-present<br />Powered by self
---

## 轻松上手

快速创建一个简单的应用

```bash
// 创建一个目录是 my-app 的应用
yarn create parsec-app my-app

// 项目类型选择Admin
> Admin

// 预览项目
$ cd my-app && yarn start
```
