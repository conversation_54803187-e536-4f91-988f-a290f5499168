import { RouteComponentProps as RCPs, RouteProps } from 'react-router-dom';

/**
 * 路由配置
 */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
export interface RouteConfig<R = any> extends RouteProps {
  /**
   * 控制左边菜单的层级显示
   */
  children?: RouteConfig<R>[];
  /**
   * 菜单名字
   */
  name: string;
  /**
   * 菜单栏图标
   */
  icon?: React.ReactNode;
  /**
   * 重新进入页面保持上一次的状态
   */
  keep?: boolean;
  /**
   * 详情页设置这个，可以防止父级页面重新加载
   */
  isDetail?: true;
  /**
   * 是否在菜单栏显示
   */
  inMenu?: false;
  /**
   * 路由id
   */
  routeId?: number | string | number[] | string[];
  /**
   * 角色数组，可以用APP的permissions来鉴权
   */
  roles?: R[];
  /**
   * 自定义渲染菜单项
   * 例子：({ name, icon }) => (
   *  <div>
   *    {icon}
   *    <span>{name}</span>
   *  </div>
   *)
   */
  menuRender?: (config: RouteConfig<R>) => React.ReactNode;
  /**
   * 微应用名称，跟在微应用webpack设置的保持一致
   */
  microAppName?: string;
  /**
   * 微前端微应用url地址
   */
  microAppUrl?: string;
  /**
   * 微前端微应用属性
   */
  microAppProps?: { [name: string]: any };
}

/**
 * 路由页面props
 */
export interface RouteComponentProps<T = any> extends RCPs<T> {
  reloadTableList: (tableName?: string) => void;
}

/**
 * 跟后端约定的列表请求数据
 */
export interface ListApiRequestParams {
  limit?: number;
  page?: number;
}

/**
 * 跟后端约定的列表返回数据
 */
export interface ListApiResponseData<D> {
  list: D[];
  total: number;
  pageNum: number;
}
