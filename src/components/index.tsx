/**
 * 列表操作项，超过3个会隐藏多余的
 */
export { default as ActionsWrap } from './actionsWrap';
/**
 * 传数组就会返回Select
 */
export { default as ArrSelect } from './arrSelect';
/**
 * 自动加载数据的表格
 */
export { default as BaseTable } from './baseTable';
/**
 * 带全选的多选框
 */
export { default as CheckboxGroup } from './checkboxGroup';
/**
 * 选择的时间会设置为当天的00:00:00
 */
export { default as DayRangePicker } from './dayRangePicker';
/**
 * 富文本编辑器
 */
export { default as Editor } from './editor';
/**
 * 页尾
 */
export { default as Footer } from './footer';
/**
 * 传items就能渲染一个表单，更方便设置初始值和取值
 */
export { default as Form } from './form';
export * from './form';
/**
 * 可以切换为描述和表单状态的详情组件
 */
export { default as FormDescriptions } from './formDescriptions';
/**
 * 弹窗表单
 */
export { default as FormModal } from './formModal';
export { default as LinkButton } from './linkButton';
export { default as MoreDropdown } from './moreDropdown';
export { default as Upload } from './upload';
/**
 * 可以显示图片列表的上传组件
 */
export { default as UploadImg } from './uploadImg';
/**
 * 上传按钮
 */
export { default as UploadBtn } from './uploadBtn';
export { default as PageHeader, useGetBreadcrumbs } from './pageHeader';
/**
 * 简写大数据，比如10000会简写成1万
 */
export { default as StatisticalStr } from './statisticalStr';
/**
 * 选择的时间会设置为当天的00:00:00
 */
export { default as DayPicker } from './dayPicker';
/**
 * 显示格式化后的时间
 */
export { default as DateShow } from './dateShow';
/**
 * 表单页面固定在底部的操作栏
 */
export { default as FixedFormActions } from './fixedFormActions';
/**
 * 预览图片的Modal
 */
export { default as PreviewImgModal } from './previewImgModal';
/**
 * 可以预览的图片组件
 */
export { default as PreviewImg } from './previewImg';
/**
 * 用于输入以分为单位的InputNumber
 */
export { default as InputMoney } from './inputMoney';
/**
 * 在formItem里需要再嵌套几个item时可以用这个包裹
 */
export { default as InlineFormItemWrap } from './inlineFormItemWrap';
/**
 * 单独使用组件不包裹App的话，react-router报错，就可以用这个
 */
export { Router } from 'react-router';
/**
 * 表单上使用腾讯地图，选择地址
 */
export { default as QQMap } from './qqmap';
/**
 * 转换表单组件事件和值
 */
export { default as Normalize } from './normalize';
/**
 * 水印组件
 */
export { default as WaterMark } from './waterMark';
/**
 * 海报编辑器
 */
export { default as PostEditor } from './postEditor';
/**
 * radio组件
 */
export { default as ArrRadio } from './arrRadio';
/**
 * 选择表单项
 */
export { default as SelectFormItem } from './selectFormItem';
