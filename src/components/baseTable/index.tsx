import React, {
  useEffect,
  useCallback,
  useRef,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Input, Table, Button, Popconfirm } from 'antd';
import { TablePaginationConfig, TableProps } from 'antd/lib/table';
import {
  ColumnGroupType,
  ColumnType,
  Key,
  SorterResult,
  TableCurrentDataSource,
} from 'antd/lib/table/interface';
import RoutesTreeStore from '../../stores/RoutesTreeStore';
import {
  useEffectState,
  usePromise,
  useRefState,
  useStateRef,
} from 'parsec-hooks';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
  arrayMove,
} from 'react-sortable-hoc';
import { MenuOutlined, PlusOutlined, SaveOutlined } from '@ant-design/icons';
import MyForm, { MyFormItem, MyFormItemObjProps } from '../form';
import './index.less';
import LinkButton from '../linkButton';
import { useWhyDidYouUpdate } from 'ahooks';

export interface TableOnChangeParams<D> {
  pagination: TablePaginationConfig;
  filters: Record<string, Key[] | null>;
  sorter: SorterResult<D> | SorterResult<D>[];
  extra: TableCurrentDataSource<D>;
}

export interface GetListParams<D, P> extends TableOnChangeParams<D> {
  params: P & { [N in keyof D]: D[N] };
}

export interface BaseTableProps<D, P> extends Omit<TableProps<D>, 'columns'> {
  columns?: ((ColumnGroupType<D> | ColumnType<D>) & {
    /**
     * 编辑依赖的字段，用于优化性能
     */
    editDeps?: string[];
    /**
     * 是否可编辑，和自定义编辑的render
     */
    edit?:
      | ((
          data: D[keyof D],
          record: D,
          index: number,
          records: D[],
          form: {
            setCurrentRow: (data: D) => void;
          },
        ) => boolean | React.ReactNode)
      | boolean;
    /**
     * 编辑的表单配置
     */
    editFormItemProps?: MyFormItemObjProps<any>;
  })[];
  getList?: (
    params: GetListParams<D, { [N in keyof P]?: P[N] }>,
  ) => Promise<{ list?: D[]; total?: number }>;
  params?: { [N in keyof P]?: P[N] };
  name?: string;
  /**
   * 是否进行请求
   * @default true
   */
  needGet?: boolean;
  /**
   * 是否拖拽排序
   */
  sortable?:
    | boolean
    | ((data: D[keyof D], record: D, index: number, list: D[]) => boolean);
  /**
   * 拖拽排序列配置
   */
  sortColumn?: ColumnType<D>;
  /**
   * 排序结束回调
   */
  onSortEnd?: (data: { oldIndex: number; newIndex: number }) => void;
  /**
   * 是否是编辑模式
   */
  edit?: boolean;
  /**
   * 是否隐藏编辑按钮
   */
  editSave?: boolean;
  /**
   * 编辑模式是否可以删除
   */
  editDelete?:
    | ((data: D[keyof D], record: D, index: number, list: D[]) => boolean)
    | boolean;
  /**
   * 保存事件
   */
  onSave?: (data: {
    list: D[];
    changedList: D[];
    addedList: D[];
    deletedList: D[];
  }) => Promise<any>;
  /**
   * 添加项的初始值
   */
  addInitValue?: any;
}

export interface BaseTableRefObj<D, P> extends BaseTableProps<D, P> {
  tableOnChangeParams: TableOnChangeParams<D>;
}

const DragHandle = SortableHandle(() => (
  <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />
));
const SortableItem = SortableElement(props => <tr {...props} />);
const SortableContainerWrap = SortableContainer(props => <tbody {...props} />);

const BaseTable: <D, P>(
  props: BaseTableProps<D, P> & {
    ref?: React.MutableRefObject<BaseTableRefObj<D, P>>;
  },
) => React.ReactElement = forwardRef(function<D, P = {}>(
  props: BaseTableProps<D, P>,
  ref: React.Ref<BaseTableRefObj<D, P>>,
) {
  const {
    getList,
    params: params2,
    pagination,
    name,
    columns = [],
    needGet = true,
    editDelete = true,
    sortable,
    edit,
    editSave = true,
    addInitValue,
    sortColumn,
    onSave = useCallback(() => Promise.resolve(), []),
    rowKey = useCallback((record: D & any) => {
      return record[Object.keys(record).find(key => !!key.match(/id/i)) || ''];
    }, []),
  } = props;
  const [dataSource, setDataSource, dataSourceRef] = useRefState<D[]>([]);
  const [newDataSource, setNewDataSource] = useEffectState(dataSource);
  const [total, setTotal] = useRefState(0);
  const [loading, setLoading] = useRefState(false);
  const [params, setParams] = useRefState<TableOnChangeParams<D>>({
    pagination: {
      current: 1,
      pageSize: pagination ? pagination.defaultPageSize : 10,
      ...pagination,
    },
    filters: {} as any,
    sorter: {
      column: {},
      order: 'descend',
      field: '',
      columnKey: '',
    },
    extra: {
      currentDataSource: [],
      action: 'paginate',
    },
  });
  useImperativeHandle(
    ref,
    () => ({
      ...props,
      tableOnChangeParams: {
        ...params,
        params: params2,
      },
    }),
    [props, params, params2],
  );
  const wrappedGetList: any = useCallback(
    params =>
      getList
        ? getList(params)
        : Promise.resolve({
            list: props.dataSource || [],
            total: props.dataSource ? props.dataSource.length : 0,
          }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [props.dataSource],
  );
  const {
    reloadState,
    reloadListName,
    setReloadListName,
  } = RoutesTreeStore.useContainer();
  const preParams2 = useRef(params2);
  const loadedRef = useRef(false);
  const deletedListRef = useRef<D[]>([]);
  const params2Str = JSON.stringify(params2 || {});
  useEffect(() => {
    if (!needGet) {
      return;
    }
    if (
      ((reloadListName && reloadListName !== name) ||
        (name && !reloadListName)) &&
      loadedRef.current &&
      reloadListName !== 'all'
    )
      return;
    setLoading(true);
    deletedListRef.current = [];
    const params2 = JSON.parse(params2Str);
    if (JSON.stringify(preParams2.current) !== JSON.stringify(params2)) {
      preParams2.current = params2;
      return setParams({
        ...params,
        pagination: { ...params.pagination, current: 1 },
      });
    }
    wrappedGetList({ ...params, params: { ...(params2 || {}) } })
      .then(({ list = [], total = list.length }) => {
        loadedRef.current = true;
        const {
          pagination: { current = 1 },
        } = params;
        setTotal(total);
        if (list.length === 0 && current !== 1) {
          return setParams({
            ...params,
            pagination: { ...params.pagination, current: current - 1 },
          });
        }
        setDataSource(list);
      })
      .finally(() => setLoading(false));
  }, [
    params,
    params2Str,
    wrappedGetList,
    reloadState,
    reloadListName,
    name,
    setLoading,
    setParams,
    setTotal,
    setDataSource,
    dataSourceRef,
    setReloadListName,
    needGet,
  ]);
  const DraggableBodyRow = useCallback(
    ({ className, style, ...restProps }) => {
      // function findIndex base on Table rowKey props and should always be a right array index
      const index = (edit ? newDataSource : dataSource).findIndex(record => {
        return (
          (typeof rowKey === 'string' ? record[rowKey] : rowKey(record)) ===
          restProps['data-row-key']
        );
      });
      return <SortableItem index={index} {...restProps} />;
    },
    [dataSource, edit, newDataSource, rowKey],
  );
  const [form] = MyForm.useForm();
  const getKeyString = useCallback(
    (record: D) => {
      return typeof rowKey === 'string'
        ? rowKey
        : Object.keys(record).find(key => !!key.match(/id/i));
    },
    [rowKey],
  );
  const getSaveValues = useCallback(
    (v: any) => {
      const list: any = [];
      Object.keys(v).forEach(key => {
        const [idKey, id, dataIndex] = key.split('|');
        if (!list.find(item => item[idKey] + '' === id)) {
          list.push({
            ...dataSource.find(item => item[idKey] + '' === id),
            [idKey]: id,
          });
        }
        const find = list.find(item => item[idKey] + '' === id);
        if (find) {
          find[dataIndex] = v[key];
        }
      });
      const changedList: any = [];
      const addedList: any = [];
      list.forEach((item, index) => {
        const key = getKeyString(item);
        const target = {
          ...dataSource.find(data => data[key] + '' === item[key]),
        };
        target[key] = target[key] + '';
        if (item['_id']) {
          addedList.push(item);
        } else if (JSON.stringify(target) !== JSON.stringify(item)) {
          changedList.push(item);
        }
      });
      return {
        list,
        changedList,
        addedList,
        deletedList: deletedListRef.current,
      };
    },
    [dataSource, getKeyString],
  );
  const { handle: handleSave, loading: saveLoading } = usePromise(onSave, {
    needInit: false,
  });
  const onSortEnd = useCallback(
    ({ oldIndex, newIndex }) => {
      if (oldIndex !== newIndex) {
        const values = getSaveValues(form.getFieldsValue());
        const newData = arrayMove(
          [].concat(edit ? values.list : dataSource),
          oldIndex,
          newIndex,
        ).filter(el => !!el);
        props.onSortEnd?.({ oldIndex, newIndex });
        (edit ? setNewDataSource : setDataSource)(() => newData);
      }
    },
    [
      getSaveValues,
      form,
      edit,
      dataSource,
      props,
      setNewDataSource,
      setDataSource,
    ],
  );
  const DraggableContainer = useCallback(
    props => (
      <SortableContainerWrap
        useDragHandle
        disableAutoscroll
        helperClass="row-dragging"
        onSortEnd={onSortEnd}
        {...props}
      />
    ),
    [onSortEnd],
  );

  return (
    <MyForm
      form={form}
      onSubmit={v => {
        handleSave(getSaveValues(v)).then(() => {
          setReloadListName(name);
        });
        return Promise.resolve();
      }}
      layout={false}
      formProps={{
        component: false,
        layout: 'inline',
      }}
      data={useMemo(() => {
        const data = {};
        newDataSource.forEach((item, i) => {
          columns.forEach(({ dataIndex }: any) => {
            if (dataIndex) {
              const idKey = getKeyString(item);
              data[`${idKey}|${item[idKey]}|${dataIndex}`] = item[dataIndex];
            }
          });
        });
        return data;
        // eslint-disable-next-line react-hooks/exhaustive-deps
      }, [!edit && columns, getKeyString, newDataSource])}
      submitButton={false}
    >
      <Table
        className={'baseTable'}
        loading={loading}
        dataSource={edit ? newDataSource : dataSource}
        onChange={(...arg) => {
          const [pagination, filters, sorter, extra] = arg;
          setReloadListName(name);
          setParams({
            pagination: pagination as any,
            filters: filters as any,
            sorter,
            extra,
          });
          props.onChange && props.onChange(...arg);
        }}
        {...props}
        rowKey={rowKey}
        columns={[
          ...(sortable
            ? [
                {
                  title: '排序',
                  dataIndex: 'sort',
                  width: 80,
                  className: 'drag-visible',
                  render: (record, records, index) =>
                    typeof sortable === 'function' ? (
                      sortable(record, records, index, newDataSource) && (
                        <DragHandle />
                      )
                    ) : (
                      <DragHandle />
                    ),
                  ...sortColumn,
                },
              ]
            : []),
          ...columns.map(
            (
              { edit: itemEdit = true, editFormItemProps, editDeps, ...option },
              j,
            ) => {
              return {
                ...option,
                render: (...arg: any) => {
                  const oldRender = option.render
                    ? (option.render as any)(...arg)
                    : arg[0] === undefined
                    ? '-'
                    : arg[0];
                  const input = (
                    <Input
                      placeholder={
                        typeof option.title === 'string'
                          ? `请输入${option.title}`
                          : undefined
                      }
                    />
                  );
                  const idKey = getKeyString(arg[1]);
                  const formPrefix = `${idKey}|${arg[1][idKey]}`;
                  return edit ? (
                    option.title === '操作' ? (
                      (typeof editDelete === 'function'
                        ? (editDelete as any)(...arg, newDataSource)
                        : editDelete) && (
                        <Popconfirm
                          title="确定要删除吗？"
                          onConfirm={() => {
                            const values = getSaveValues(form.getFieldsValue());
                            const newDataSource = values.list;
                            deletedListRef.current.push(
                              newDataSource.splice(arg[2], 1)[0],
                            );
                            setNewDataSource([...newDataSource]);
                          }}
                          okText="确定"
                          cancelText="取消"
                        >
                          <LinkButton>删除</LinkButton>
                        </Popconfirm>
                      )
                    ) : (
                      <MyFormItem
                        name={`${formPrefix}|${(option as any).dataIndex}`}
                        renderShouldUpdate={(v1, v2) => {
                          const list1 = getSaveValues(v1).list;
                          const list2 = getSaveValues(v2).list;
                          if (editDeps?.length) {
                            const getList = (list: typeof list1) => {
                              const filterList = [];
                              list.forEach(obj => {
                                const newObj = {};
                                Object.keys(obj)
                                  .filter(key => editDeps.includes(key))
                                  .forEach(key => {
                                    newObj[key] = obj[key];
                                  });
                                filterList.push(newObj);
                              });
                              return filterList;
                            };
                            return (
                              JSON.stringify(getList(list1)) !==
                              JSON.stringify(getList(list2))
                            );
                          } else
                            return (
                              JSON.stringify(list1) !== JSON.stringify(list2)
                            );
                        }}
                        render={
                          typeof itemEdit === 'function'
                            ? () => {
                                const values = getSaveValues(
                                  form.getFieldsValue(),
                                );
                                return itemEdit(
                                  arg[0],
                                  values.list[arg[2]] || {},
                                  arg[2],
                                  values.list,
                                  {
                                    ...form,
                                    setCurrentRow: ({ ...values }: any) => {
                                      Object.keys(values).forEach(name => {
                                        values[`${formPrefix}|${name}`] =
                                          values[name];
                                        delete values[name];
                                      });
                                      form.setFieldsValue(values);
                                    },
                                  },
                                ) as any;
                              }
                            : itemEdit === true
                            ? input
                            : itemEdit || oldRender
                        }
                        {...editFormItemProps}
                        formItemProps={{
                          style: { marginBottom: 0 },
                          ...editFormItemProps?.formItemProps,
                        }}
                      />
                    )
                  ) : (
                    oldRender
                  );
                },
              };
            },
          ),
        ]}
        footer={
          edit
            ? () => (
                <>
                  {editSave ? (
                    <Button
                      type={'primary'}
                      block
                      icon={<SaveOutlined />}
                      style={{ marginBottom: 10 }}
                      onClick={() => form.submit()}
                      loading={saveLoading}
                    >
                      保存
                    </Button>
                  ) : (
                    <></>
                  )}
                  <Button
                    type={'dashed'}
                    block
                    icon={<PlusOutlined />}
                    onClick={() => {
                      const values = getSaveValues(form.getFieldsValue());
                      const newDataSource = values.list;
                      setNewDataSource([
                        ...newDataSource,
                        { _id: Math.random(), ...addInitValue },
                      ] as any);
                    }}
                  >
                    添加
                  </Button>
                </>
              )
            : props.footer
        }
        components={{
          ...props.components,
          body: sortable
            ? {
                ...props.components?.body,
                wrapper: DraggableContainer,
                row: DraggableBodyRow,
              }
            : props.components?.body,
        }}
        pagination={
          pagination !== false && !edit
            ? {
                ...params.pagination,
                total,
                ...pagination,
              }
            : false
        }
      />
    </MyForm>
  );
});

export default BaseTable;
