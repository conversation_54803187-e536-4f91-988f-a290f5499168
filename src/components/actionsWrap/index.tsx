import React, { Fragment, isValidElement } from 'react';
import { Divider, Menu } from 'antd';
import MoreDropdown from '../moreDropdown';

/**
 * 列表操作项，超过3个会隐藏多余的
 * @param children
 * @param max
 */
export default ({
  children,
  max = 3,
}: {
  /**
   * children建议放多个LinkButton
   */
  children: React.ReactNodeArray | React.ReactNode;
  /**
   * 最多显示几个操作
   */
  max?: number;
}) => {
  children = (children instanceof Array ? children : [children]).filter(
    item => item,
  );
  return (
    children instanceof Array && (
      <>
        {children.map((child, index) => {
          if (!(children instanceof Array)) return null;
          const num = children.length > max ? max - 1 : max;
          return (
            index < num && (
              <Fragment key={index}>
                {child}
                {children.length !== index + 1 && <Divider type={'vertical'} />}
              </Fragment>
            )
          );
        })}
        {children.length > max && (
          <MoreDropdown
            overlay={
              <Menu>
                {[...children]
                  .splice(max - 1, children.length)
                  .map((child, index) => {
                    let props;
                    if (isValidElement(child)) {
                      props = child.props;
                    }
                    return <Menu.Item key={index} {...props} />;
                  })}
              </Menu>
            }
          />
        )}
      </>
    )
  );
};
