---
nav:
  title: 组件
---

# 操作项

通常跟 [LinkButton](/components/link-button) 使用，会对多个 button 用分割线隔开。

```tsx
import React from 'react';
import { LinkButton, ActionsWrap } from 'parsec-admin';

export default () => (
  <ActionsWrap>
    <LinkButton>编辑</LinkButton>
    <LinkButton>详情</LinkButton>
    <LinkButton>删除</LinkButton>
  </ActionsWrap>
);
```

## 多个操作项

当大于 4 个操作项时，多余的会被隐藏。

```tsx
import React from 'react';
import { LinkButton, ActionsWrap } from 'parsec-admin';

export default () => (
  <ActionsWrap>
    <LinkButton>编辑</LinkButton>
    <LinkButton>详情</LinkButton>
    <LinkButton>删除</LinkButton>
    <LinkButton>选择</LinkButton>
  </ActionsWrap>
);
```

## 自定义显示

超过 2 个时隐藏。

```tsx
import React from 'react';
import { LinkButton, ActionsWrap } from 'parsec-admin';

export default () => (
  <ActionsWrap max={2}>
    <LinkButton>编辑</LinkButton>
    <LinkButton>详情</LinkButton>
    <LinkButton>删除</LinkButton>
    <LinkButton>选择</LinkButton>
  </ActionsWrap>
);
```

## API

| 属性     | 说明                       | 默认值 |
| -------- | -------------------------- | ------ |
| max      | 最多显示几个操作项。       | 3      |
| children | 被包含的子项，至少是两个。 | -      |
