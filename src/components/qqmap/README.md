# 地图定位

可以直接在 FormItem 里使用的地图定位选择组件。

```tsx
import React from 'react';
import { Form, QQMap } from 'parsec-admin';

export default () => (
  <Form
    items={[
      {
        name: 'gps',
        required: true,
        label: '医院地址',
        formItemProps: {
          initialValue: {
            address: '重庆市渝北区红锦大道89号',
            lat: 29.595399,
            lng: 106.521407,
          },
        },
        render: <QQMap />,
      },
    ]}
    layout={{
      labelCol: {
        span: 4,
      },
    }}
  />
);
```

## API

| 属性     | 说明                                                              | 默认值 |
| -------- | ----------------------------------------------------------------- | ------ |
| key      | 可以设置自己的腾讯地图 key ，具体部署的项目需要加域名白名单       | -      |
| onChange | (value) => void                                                   | -      |
| value    | {address?:string; lat?:number 或 string; lng?: number 或 string;} | -      |
