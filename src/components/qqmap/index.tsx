import React, { useState, useCallback, useRef } from 'react';
import axios from 'axios';
import { Input, Row, Col } from 'antd';
import { useImportCDN } from 'parsec-hooks';
import markerIcon from './img/marker10.png';

const { Search } = Input;
type MapValue = {
  address?: string;
  addressName?: string;
  lat?: string | number;
  lng?: string | number;
};
interface Props {
  key?: string;
  value?: MapValue;
  onChange?: (v: MapValue) => void;
}
export default ({
  key = 'UNDBZ-L4V3I-AU7GW-573FE-LOENF-YZBLI',
  value,
  onChange,
}: Props) => {
  const [keywords, setKeyWords] = useState(''); //查询关键字
  const [flagIndex, setFlagIndex] = useState(null); //当前选中的列表项
  const [resultList, setResultList] = useState(null); // 查询结果
  const initMaker: any = useRef(); //初始化地图组件时，给定的一个maker
  const myMap: any = useRef(); // 全局地图
  const mysearchService: any = useRef(); //地图检索服务
  const myMarkers: any = useRef([]); // maker集合
  const listenerArr: any = useRef([]); //maker监听事件集合
  const dom = useRef();
  //设置maker
  const setAnchor = useCallback((marker, flag) => {
    const left = marker.index * 27;
    const qq = (window as any).qq;
    if (qq) {
      if (flag === true) {
        const anchor = new qq.maps.Point(10, 30);
        const origin = new qq.maps.Point(left, 0);
        const size = new qq.maps.Size(27, 33);
        const icon = new qq.maps.MarkerImage(markerIcon, size, origin, anchor);
        marker.setIcon(icon);
      } else {
        const anchor = new qq.maps.Point(10, 30);
        const origin = new qq.maps.Point(left, 35);
        const size = new qq.maps.Size(27, 33);
        const icon = new qq.maps.MarkerImage(markerIcon, size, origin, anchor);
        marker.setIcon(icon);
      }
    }
  }, []);
  //选择结果
  const selectData = useCallback(
    (item, index) => {
      console.log(item);
      setFlagIndex(index);
      Array.from(myMarkers.current).map((marker: any, n) => {
        console.log(n, index);
        if (n === index) {
          setAnchor(marker, false);
          marker.setZIndex(10);
        } else {
          if (!marker.isClicked) {
            setAnchor(marker, true);
            marker.setZIndex(9);
          }
        }
      });
      onChange &&
        onChange({
          address: item.address,
          lat: item.latLng.lat,
          lng: item.latLng.lng,
          addressName: item.name,
        });
    },
    [onChange, setAnchor],
  );
  //搜索地图
  const searchMap = useCallback((params: any) => {
    axios
      .get(
        `https://apis.map.qq.com/ws/place/v1/search?boundary=nearby(${params.lat},${params.lng},1000)&keyword=${params.keywords}&page_size=10&page_index=1&key=${params.key}`,
      )
      .then(function(response) {
        // handle success
        console.log(response);
      })
      .catch(function(error) {
        // handle error
        console.log(error);
      })
      .finally(function() {
        // always executed
      });
  }, []);
  //初始化地图
  const initQQMap = useCallback(() => {
    const tarLat = value?.lat || 39.90736606309809;
    const tarLng = value?.lng || 116.39774322509766;
    console.log(value);
    const qq = (window as any).qq;
    console.log(qq);
    if (qq) {
      const myLatlng = new qq.maps.LatLng(tarLat, tarLng);
      // 设置地图属性
      const myOptions = {
        zoom: 16,
        center: myLatlng,
        mapTypeId: qq.maps.MapTypeId.ROADMAP,
      };
      // 创建地图，绑定dom

      if (dom.current) {
        // 初始化地图
        myMap.current = new qq.maps.Map(dom.current, myOptions);

        // 调用检索
        const latlngBounds = new qq.maps.LatLngBounds();
        // 调用Poi检索类
        let markers: any = []; //用户搜索后显示的点的集合
        // 初始化的时候如果有值，给一个初始化maker
        initMaker.current = new qq.maps.Marker({
          map: myMap.current,
          position: myLatlng,
        });
        // 调用搜索服务
        mysearchService.current = new qq.maps.SearchService({
          complete: (results: any) => {
            myMarkers.current = [];
            markers = [];
            const pois = results.detail.pois;
            setResultList(pois);
            for (let i = 0, l = pois.length; i < l; i++) {
              const poi = pois[i];
              latlngBounds.extend(poi.latLng);
              const marker = new qq.maps.Marker({
                map: myMap.current,
                position: poi.latLng,
                zIndex: 10,
              });

              marker.index = i;
              marker.isClicked = false;
              setAnchor(marker, true);
              const listener3 = qq.maps.event.addDomListener(
                marker,
                'click',
                () => {
                  selectData(pois?.[i], i);
                },
              );
              listenerArr.current.push(listener3);
              markers.push(marker);
            }
            myMap.current.fitBounds(latlngBounds);
            myMarkers.current = markers;
            //如果有检索结果，默认选择第一个
            if (pois.length > 0) {
              selectData(pois?.[0], 0);
            }
          },
        });
        //新版调用服务
        searchMap({
          lat: tarLat,
          lng: tarLng,
          keywords: keywords,
          key: key,
        });
      }
    }
  }, [key, keywords, searchMap, selectData, setAnchor, value]);
  //按名字检索
  const searchKeyword = useCallback(() => {
    //清除初始maker
    initMaker.current.setMap(null);
    // 清空上一次搜索结果
    Array.from(myMarkers.current).forEach((marker: any, index) => {
      marker.setMap(null);
      const qq = (window as any).qq;
      if (qq) {
        qq.maps.event.removeListener(listenerArr.current[index]);
      }
    });
    mysearchService.current.setLocation('重庆');
    mysearchService.current.search(keywords);
  }, [keywords]);
  useImportCDN(
    `//map.qq.com/api/js?v=2.exp&key=${key}&callback=qqMapCallBack`,
    'qq',
    () => {
      window['qqMapCallBack'] = initQQMap;
    },
  );
  return (
    <>
      <Row gutter={16} style={{ padding: '5px 0' }}>
        <Col span={12}>
          当前坐标：{value?.lat ? value?.lat + ',' + value?.lng : '请选择'}
        </Col>
        <Col span={12}>当前地址：{value?.address ? value?.address : '-'}</Col>
      </Row>
      <Search
        placeholder="请输入地址搜索"
        onChange={e => {
          setKeyWords(e.target.value);
        }}
        onSearch={searchKeyword}
        enterButton
        style={{ marginBottom: 10 }}
      />
      <Row
        style={{
          marginTop: 10,
          border: '1px solid #ccc',
        }}
      >
        <Col span={6} style={{ height: 400, overflowY: 'auto' }}>
          {!resultList ? (
            <div style={{ padding: 10 }}>
              <h4>使用说明：</h4>
              <p>
                在搜索框搜索关键词后，地图上会显示相应poi点，同时左侧显示对应该点的信息，点击列表上信息，顶部会显示相应该点的坐标和地址。
              </p>
            </div>
          ) : (
            (resultList || []).map((x: any, index) => {
              return (
                <div
                  key={x.id}
                  style={{
                    padding: 10,
                    borderBottom: '1px dashed #ccc',
                    cursor: 'pointer',
                    fontSize: 12,
                    background:
                      flagIndex === index ? 'rgb(219, 228, 242)' : 'none',
                  }}
                  onClick={() => selectData(x, index)}
                >
                  <h4>
                    {index + 1}、{x.name}
                  </h4>
                  <p style={{ marginBottom: 2 }}>地址：{x.address}</p>
                  <p style={{ marginBottom: 2 }}>电话：{x.phone}</p>
                  <p style={{ marginBottom: 2 }}>
                    坐标：{x.latLng.lat} , {x.latLng.lng}
                  </p>
                </div>
              );
            })
          )}
        </Col>
        <Col span={18}>
          <div ref={dom} style={{ height: 400, width: '100%' }}></div>
        </Col>
      </Row>
    </>
  );
};
