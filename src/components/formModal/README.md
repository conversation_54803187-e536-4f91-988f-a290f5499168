# 弹窗表单

在 Modal 里使用的表单，一般调用 useModal 来使用它，常用在表单操作项。

```tsx
import React, { useMemo } from 'react';
import { Button, Space, Form, Radio, Input } from 'antd';
import { useModal, PortalProvider, ArrSelect } from 'parsec-admin';

const Demo = () => {
  const [form] = Form.useForm();
  const switchModalVisible = useModal(
    {
      title: '新增用户',
      form,
      items: [
        {
          label: '姓名',
          name: 'name',
          required: true,
        },
        ({ name = '' }) => ({
          label: `${name}的年龄`,
          name: 'age',
          required: true,
        }),
        {
          label: `自定义xxx`,
          name: 'customize',
          render: (
            <Radio.Group
              onChange={e => {
                if (e.target.value) {
                  form.resetFields(['xxx']);
                } else {
                  form.setFieldsValue({ xxx: 123 });
                }
              }}
            >
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          ),
        },
        {
          label: `xxx`,
          name: 'xxx',
          render: (_, {customize}) => (
            <Input disabled={!customize}/>
          ),
        },
        {
          label: '性别',
          name: 'sex',
          labelName: 'sexName',
          render: <ArrSelect options={[{value: 1, label: '男'}, {value: 2, label: '女'}]}/>
        }
      ],
      onSubmit: (values) => {
        alert(JSON.stringify(values));
        return new Promise(resolve => setTimeout(resolve, 1000))
      },
    },
    // 如果modal里用了state，请在下面添加依赖
    [],
  );
  return (
    <Space>
      <Button type={'primary'} onClick={() => switchModalVisible()}>
        显示 Modal
      </Button>
      <Button
        type={'primary'}
        onClick={() => switchModalVisible({ name: '姓名', age: '18' })}
      >
        填充值
      </Button>
    </Space>
  );
};

export default () => (
  <PortalProvider>
    <Demo />
  </PortalProvider>
);
```

## API

| 属性        | 说明                                                                  | 默认值              |
| ----------- | --------------------------------------------------------------------- | ------------------- |
| form        | 自定义 form 实例。                                                    | `form.useForm()[0]` |
| onSubmit    | 提交事件，当 form 检验完后会触发这个事件，需要返回一个 Promise 方法。 | -                   |
| items       | 表单项。                                                              | -                   |
| myFormProps | form [props](/components/form#api) 。                                 | -                   |

> 更多用法可以查看 [Modal](https://ant.design/components/modal-cn/#API) 。
