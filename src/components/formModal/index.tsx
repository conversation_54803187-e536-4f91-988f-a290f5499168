import MyForm, { MyFormProps, MyFormItemProps } from '../form';
import { Modal, Form } from 'antd';
import { ModalProps } from 'antd/lib/modal';
import React, { useCallback, useEffect, useState } from 'react';
import { FormInstance } from 'antd/lib/form/hooks/useForm';
import styled from 'styled-components';
import { getPrefixCls } from '../../_utils';

export interface FormModalProps<D> extends ModalProps {
  form?: FormInstance;
  onSubmit?: (values: D) => Promise<any>;
  items?: MyFormItemProps<D>[];
  myFormProps?: Omit<MyFormProps<D>, 'items'>;
  children?: React.ReactNode;
  /**
   * 关闭弹窗是重置表单
   * @default true
   */
  closeReset?: boolean;
}

export default function<D>({
  form = Form.useForm()[0],
  onSubmit,
  items = [],
  myFormProps,
  closeReset = true,
  ...props
}: FormModalProps<D>) {
  const { validateFields, resetFields } = form;
  const [submitLoading, setSubmitLoading] = useState(false);
  const handleSubmit = useCallback(
    (e: React.MouseEvent) => {
      e?.preventDefault?.();
      validateFields().then(async values => {
        setSubmitLoading(true);
        if (onSubmit) {
          await onSubmit(values as D).finally(() => setSubmitLoading(false));
        }
        setSubmitLoading(false);
      });
    },
    [onSubmit, validateFields],
  );
  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    form.submit = handleSubmit;
  }, [form, handleSubmit]);
  return (
    <Modal
      width={640}
      afterClose={closeReset ? resetFields : undefined}
      onOk={handleSubmit}
      confirmLoading={submitLoading}
      children={
        <ModalFormWrap>
          <MyForm layout={'modal'} form={form} items={items} {...myFormProps} />
        </ModalFormWrap>
      }
      {...props}
      bodyStyle={{ padding: '28px 24px 20px', ...props.bodyStyle }}
    />
  );
}

const ModalFormWrap = styled.div`
  .${getPrefixCls}-form-item {
    margin-bottom: 12px;
  }
`;
