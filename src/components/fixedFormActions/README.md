# 页尾操作栏

在 [高级表单](https://preview.pro.ant.design/form/advanced-form) 中，操作项过多，提交按钮就适合放在页尾。

```tsx
import React from 'react';
import { FixedFormActions, Form } from 'parsec-admin';
import { Button, Space } from 'antd';

export default () => {
  const [form] = Form.useForm();
  return (
    <>
      <Form
        form={form}
        submitButton={false}
        items={[
          {
            label: '姓名',
            name: 'name',
            required: true,
            formItemProps: {
              extra: '提交按钮在页面最下面',
            },
          },
        ]}
      />
      <FixedFormActions>
        <Space>
          <Button onClick={() => form.resetFields()}>重置</Button>
          <Button type={'primary'} onClick={() => form.validateFields()}>
            提交
          </Button>
        </Space>
      </FixedFormActions>
    </>
  );
};
```

## API

| 属性     | 说明   | 默认值 |
| -------- | ------ | ------ |
| children | 子项。 | -      |
