import React from 'react';
import styled from 'styled-components';

export interface FixedFormActionsProps
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLElement>,
    HTMLElement
  > {
  children: React.ReactNode;
}

export default styled(({ className, children }: FixedFormActionsProps) => (
  <footer className={className}>{children}</footer>
))`
  z-index: 1;
  position: fixed;
  left: 0;
  padding: 0 24px;
  box-sizing: border-box;
  bottom: 0;
  width: 100%;
  height: 56px;
  background-color: var(--component-background);
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);
  display: flex;
  align-items: center;
  justify-content: flex-end;
`;
