# 预览图片

点击后可以预览的图片的组件。

```tsx
import React from 'react';
import { PreviewImg } from 'parsec-admin';

export default () => (
  <PreviewImg
    src={'https://gw.alipayobjects.com/zos/rmsportal/rlpTLlbMzTNYuZGGCVYM.png'}
  />
);
```

## 其他组件控制

`children` 传入什么，界面就会显示什么，点击后就会预览图片。

```tsx
import React from 'react';
import { PreviewImg } from 'parsec-admin';
import { Button } from 'antd';

export default () => (
  <div>
    <PreviewImg
      src={'https://gw.alipayobjects.com/zos/rmsportal/rlpTLlbMzTNYuZGGCVYM.png'}
    >
      <Button type={'primary'}>显示图片</Button>
    </PreviewImg>
    <PreviewImg
      src={['https://gw.alipayobjects.com/zos/rmsportal/rlpTLlbMzTNYuZGGCVYM.png','https://gw.alipayobjects.com/zos/rmsportal/rlpTLlbMzTNYuZGGCVYM.png']}
    >
      <Button type={'primary'}>显示多张图片</Button>
    </PreviewImg>
  </div>
);
```
