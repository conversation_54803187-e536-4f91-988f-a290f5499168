import React, { useMemo, useState } from 'react';
import { Image } from 'antd';

export interface PreviewImgProps
  extends Omit<
    React.DetailedHTMLProps<
      React.ImgHTMLAttributes<HTMLImageElement>,
      HTMLImageElement
    >,
    'src'
  > {
  src: string | string[];
  children?: React.ReactNode;
  /**
   * 缩略图链接后缀
   */
  suffix?: string;
}

export default ({ src, suffix = '', children, ...props }: PreviewImgProps) => {
  const [visible, setVisible] = useState(false);
  const isArrSrc = src instanceof Array;
  const node = useMemo(
    () =>
      children || (
        <img
          src={`${isArrSrc ? src[0] : src}${suffix}`}
          alt=""
          {...props}
          style={{ cursor: 'pointer', ...props.style }}
        />
      ),
    [children, isArrSrc, props, src, suffix],
  );
  return (
    <>
      <span onClick={() => setVisible(true)}>{node}</span>
      <div style={{ display: 'none' }}>
        {isArrSrc ? (
          <Image.PreviewGroup
            preview={{ visible, onVisibleChange: setVisible }}
          >
            {(src as any).map(item => (
              <Image style={{ display: 'none' }} key={item} src={item} />
            ))}
          </Image.PreviewGroup>
        ) : (
          <Image
            src={src as any}
            preview={{ visible, onVisibleChange: setVisible }}
          />
        )}
      </div>
    </>
  );
};
