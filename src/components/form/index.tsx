import React, { useEffect, useState } from 'react';
import { Button, Input, Form } from 'antd';
import { FormItemProps, FormProps } from 'antd/lib/form';
import styled from 'styled-components';
import { ColProps } from 'antd/lib/grid';
import { FormInstance } from 'antd/lib/form/hooks/useForm';
import { useInit, useForceUpdate } from 'parsec-hooks';
import { getPrefixCls } from '../../_utils';
import { FormInstance as RCFormInstance } from 'rc-field-form';
import { FormProvider } from 'antd/lib/form/context';
import InternalForm from 'antd/lib/form/Form';
import { ShouldUpdate } from 'rc-field-form/lib/Field';
import classNames from 'classnames';
import SelectFormItem from '../../components/selectFormItem';
import { useRef } from 'react';

export interface MyFormItemObjProps<D> {
  label?: React.ReactNode;
  name?: string | string[];
  /**
   * 设置后同时会提交 select 的 option label 值
   */
  labelName?: string;
  formItemProps?: Omit<FormItemProps, 'name' | 'children'>;
  /**
   * 设置为false将不会显示
   */
  render?:
    | React.ReactElement
    | boolean
    | ((v: D[keyof D] | undefined, values: D) => React.ReactNode | false);
  /**
   * 设置为字符串会直接成为必填的错误提示
   */
  required?: boolean | string;
  /**
   * 自定义判断是否更新
   */
  renderShouldUpdate?: ShouldUpdate<D>;
}

export type MyFormItemProps<D> =
  | ((
      values: D | undefined,
      formInstance: RCFormInstance,
    ) => MyFormItemObjProps<D> | React.ReactNode)
  | MyFormItemObjProps<D>;

export interface MyFormProps<D> {
  form?: FormInstance;
  layout?:
    | 'modal'
    | 'inline'
    | false
    | {
        wrapperCol?: ColProps;
        labelCol?: ColProps;
      };
  items?: MyFormItemProps<D>[];
  formProps?: FormProps;
  submitButton?: React.ReactNode | false;
  extraButton?: ((form: FormInstance) => React.ReactNode) | React.ReactNode;
  onSubmit?: (values: D) => Promise<any>;
  children?: React.ReactNode;
  data?: D;
  /**
   * 自动缓存表单数据，未提交之前结束页面后再进入将回填数据
   */
  autoCacheKey?: string;
  /**
   * 提交后自动清空缓存
   * @default true
   */
  autoClearCache?: boolean;
}

export const modalLayoutItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 13 },
  },
};

export const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 12 },
    md: { span: 10 },
  },
};

export const inlineFormItemLayout = {
  labelCol: {},
  wrapperCol: {},
};

export default function MyForm<D>({
  items = [],
  form = Form.useForm()[0],
  formProps,
  submitButton,
  onSubmit,
  layout,
  children,
  extraButton,
  data,
  autoCacheKey,
  autoClearCache = true,
}: MyFormProps<D>) {
  const [submitLoading, setSubmitLoading] = useState(false);
  const isModal = layout === 'modal';
  const { forceUpdate } = useForceUpdate();
  useInit(forceUpdate);
  useInit(() => {
    const cacheData = localStorage[autoCacheKey];
    if (cacheData) {
      const data = JSON.parse(cacheData);
      form.setFieldsValue(data);
      formProps?.onValuesChange?.(data, data);
    }
  });
  useEffect(() => {
    const old = form.resetFields;
    form.resetFields = (...arg) => {
      old(...arg);
      if (autoCacheKey) {
        localStorage[autoCacheKey] = JSON.stringify(form.getFieldsValue());
      }
    };
  }, [autoCacheKey, form]);
  useEffect(() => {
    const old = form.validateFields;
    form.validateFields = (...arg) => {
      return old(...arg).then((...arg) => {
        if (autoCacheKey && autoClearCache) {
          localStorage.removeItem(autoCacheKey);
        }
        return Promise.resolve(...arg);
      });
    };
  }, [autoCacheKey, form]);
  const dataRef = useRef('{}');
  useEffect(() => {
    if (data && JSON.stringify(data) !== dataRef.current) {
      const preData = JSON.parse(dataRef.current);
      const values = {};
      Object.entries(data).forEach(([key, value]) => {
        if (value !== preData[key]) {
          values[key] = value;
        }
      });
      form.setFieldsValue(values);
      dataRef.current = JSON.stringify(data);
    }
  }, [data, form]);
  return (
    <Form
      requiredMark={isModal ? true : 'optional'}
      form={form}
      style={{ marginTop: isModal ? undefined : 10 }}
      {...(isModal
        ? modalLayoutItemLayout
        : layout === undefined
        ? formItemLayout
        : layout === 'inline'
        ? { ...inlineFormItemLayout, layout: 'inline' }
        : layout)}
      {...formProps}
      onValuesChange={(...arg) => {
        if (autoCacheKey) {
          localStorage[autoCacheKey] = JSON.stringify(arg[1]);
        }
        formProps?.onValuesChange?.(...arg);
      }}
      onFinish={async values => {
        setSubmitLoading(true);
        if (onSubmit) {
          await onSubmit(values as D).finally(() => setSubmitLoading(false));
        }
        if (autoCacheKey && autoClearCache) {
          localStorage.removeItem(autoCacheKey);
        }
        setSubmitLoading(false);
      }}
    >
      {items.map(props => {
        const renderItem = (
          props2: MyFormItemObjProps<D> | React.ReactNode,
        ) => {
          return (
            props2 &&
            (React.isValidElement(props2) ? (
              props2
            ) : (
              <MyFormItem
                key={((props2 as any).name || (props2 as any).label) + ''}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                {...props2}
              />
            ))
          );
        };
        return props instanceof Function ? (
          <Form.Item noStyle shouldUpdate>
            {instance => {
              return renderItem(
                props({ ...data, ...instance.getFieldsValue() }, instance),
              );
            }}
          </Form.Item>
        ) : (
          renderItem(props)
        );
      })}
      {children}
      {(!isModal || submitButton) && submitButton !== false && (
        <Form.Item
          style={{ marginTop: 32 }}
          wrapperCol={{ xs: 24, sm: { span: 10, offset: 7 } }}
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          {...(layout === 'inline'
            ? { ...inlineFormItemLayout, style: {} }
            : layout)}
        >
          {/*eslint-disable-next-line @typescript-eslint/ban-ts-comment*/}
          {/*@ts-ignore*/}
          <ExtraWrap>
            {submitButton || (
              <Button
                htmlType={'submit'}
                type={'primary'}
                loading={submitLoading}
              >
                提交
              </Button>
            )}
            {extraButton && extraButton instanceof Function
              ? extraButton(form)
              : extraButton}
          </ExtraWrap>
        </Form.Item>
      )}
    </Form>
  );
}

MyForm.useForm = Form.useForm;
MyForm.Item = Form.Item as any;
MyForm.List = Form.List;

declare type InternalFormType = typeof InternalForm;
interface FormInterface extends InternalFormType {
  useForm: typeof Form.useForm;
  Item: typeof Form.Item;
  List: typeof Form.List;
  ErrorList: typeof Form.ErrorList;
  Provider: typeof FormProvider;
  /** @deprecated Only for warning usage. Do not use. */
  create: () => void;
}

export const OldForm: FormInterface = Form;

export const MyFormItem = styled(
  <D extends unknown>({
    label,
    required,
    render,
    name,
    formItemProps,
    labelName,
    renderShouldUpdate = true,
    ...props
  }: MyFormItemObjProps<D>) => {
    name = name instanceof Array ? name.join() : name;
    const renderItem = (children: React.ReactNode) => (
      <SelectFormItem
        labelName={labelName}
        options={
          (labelName &&
            React.isValidElement(children) &&
            children.props.options) ||
          []
        }
        style={{
          display: render === false ? 'none' : undefined,
          ...formItemProps?.style,
        }}
        label={label}
        name={name}
        {...props}
        className={classNames(`form-item-${label}`, (props as any).className)}
        {...formItemProps}
        rules={
          children
            ? required
              ? [
                  {
                    required: true,
                    message: required !== true ? required : `${label}是必填的`,
                  },
                  ...(formItemProps?.rules || []),
                ]
              : formItemProps?.rules
            : undefined
        }
        children={
          React.isValidElement(children)
            ? React.cloneElement(children, {
                dropdownClassName: `down-${label}`,
                ...children.props,
              })
            : children
        }
      />
    );
    return render instanceof Function ? (
      <Form.Item shouldUpdate={renderShouldUpdate} noStyle>
        {({ getFieldsValue, getFieldValue }) =>
          renderItem(render(getFieldValue(name), getFieldsValue()))
        }
      </Form.Item>
    ) : (
      renderItem(
        [undefined, true].includes(render as any) ? (
          <Input placeholder={`请输入${label}`} />
        ) : (
          render
        ),
      )
    );
  },
)`
  .${getPrefixCls}-picker {
    width: 100%;
  }
`;

const ExtraWrap = styled.div`
  > * {
    margin-right: 8px;
  }
`;
