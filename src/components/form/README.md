# 表单

更加易用、美观，不需要自己布局的表单，默认布局为 [基础表单](https://preview.pro.ant.design/form/basic-form) 样式。

```tsx
import React from 'react';
import { Form, DayRangePicker, InlineFormItemWrap } from 'parsec-admin';
import { Input, InputNumber, Radio, Button, Divider } from 'antd';

const { TextArea } = Input;
const { Item: FormItem } = Form;

export default () => (
  <Form
    autoCacheKey={'formDemo'}
    onSubmit={values =>
      new Promise(resolve => {
        console.log(values);
        setTimeout(resolve, 1000);
      })
    }
    extraButton={({ resetFields }) => (
      <Button onClick={() => resetFields()}>重置</Button>
    )}
    items={[
      {
        label: '标题',
        name: 'title',
        render: <Input placeholder={'给目标起个名字'} />,
        required: true,
      },
      {
        label: '起止日期',
        name: 'date',
        render: <DayRangePicker />,
        required: true,
      },
      {
        label: '目标描述',
        name: 'describe',
        render: <TextArea rows={4} placeholder={'请输入你的阶段性工作目标'} />,
        required: true,
      },
      {
        label: '衡量标准',
        name: 'standard',
        render: <TextArea rows={4} placeholder={'请输入衡量标准'} />,
        required: true,
      },
      () => <Divider>可以穿插各种元素</Divider>,
      {
        label: '客户',
        name: 'client',
        render: (
          <Input placeholder={'请描述你服务的客户，内部客户直接 @姓名／工号'} />
        ),
      },
      {
        label: '邀评人',
        name: 'reviewer',
        render: <Input placeholder={'请直接 @姓名／工号，最多可邀请 5 人'} />,
      },
      {
        label: '权重',
        required: true,
        render: (
          <InlineFormItemWrap>
            <FormItem
              name={'weight'}
              rules={[{ required: true, message: '权重是必填的' }]}
            >
              <InputNumber placeholder={'请输入'} />
            </FormItem>
            <span>%</span>
          </InlineFormItemWrap>
        ),
      },
      {
        label: '目标公开',
        name: 'target',
        required: true,
        render: (
          <Radio.Group
            options={[
              { label: '公开', value: 'publicity' },
              { label: '部分公开', value: 'partlyPublic' },
              { label: '不公开', value: 'private' },
            ]}
          />
        ),
        formItemProps: {
          extra: '客户、邀评人默认被分享',
        },
      },
    ]}
  />
);
```

### 表单联动

```tsx
import React from 'react';
import { Form } from 'parsec-admin';
import { Switch, InputNumber } from 'antd';

export default () => {
  return (
    <Form
      items={[
        {
          label: '显示输入',
          name: 'show',
          formItemProps: {
            valuePropName: 'checked',
            initialValue: false,
          },
          render: <Switch />,
        },
        ({ show }) => ({
          label: '姓名',
          name: 'name',
          render: show && undefined,
        }),
        ({ show, name = '' }) => ({
          label: `${name}年龄`,
          name: 'age',
          render: show && <InputNumber />,
        }),
      ]}
    />
  );
};
```

### 表单布局

```tsx
import React from 'react';
import { Form } from 'parsec-admin';
import { Switch, InputNumber } from 'antd';

export default () => {
  return (
    <Form
      layout={'inline'}
      formProps={{requiredMark: true}}
      items={[
        {
          label: '姓名',
          name: 'name',
          required: true
        },
        {
          label: `年龄`,
          name: 'age',
          required: true,
          render: <InputNumber />,
        },
      ]}
    />
  );
};
```

## API

| 属性         | 说明                                                                                                                                               | 默认值              |
| ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------- |
| form         | 自定义 form 实例。                                                                                                                                 | `form.useForm()[0]` |
| layout       | 自定义布局，设置为 `false` 则不布局，具体可以参考 antd form [props](https://ant.design/components/form-cn/#Form) 的 `labelCol`、`wrapperCol`字段。 | -                   |
| onSubmit     | 提交事件，当 form 检验完后会触发这个事件，需要返回一个 Promise 方法。                                                                              | -                   |
| items        | 表单项。                                                                                                                                           | -                   |
| submitButton | 提交按钮，设为 `false` 则不显示。                                                                                                                  | `React.ReactNode`   |
| formProps    | antd form 的 [props](https://ant.design/components/form-cn/#Form) 。                                                                               | -                   |
| hideOptional | 如果不需要 label 上的'选填'的话，可以设置为 `true` 隐藏它                                                                                          | `false`             |
| children     | children 会放在表单之下，提交按钮之上的地方。                                                                                                      | -                   |
| extraButtons | 除了提交按钮，额外的按钮。                                                                                                                         | -                   |
| autoCacheKey | 自动缓存表单数据，未提交之前结束页面后再进入将回填数据。                                                                                           | -                   |
