import React, { useState, useMemo } from 'react';
import { FullscreenExitOutlined } from '@ant-design/icons';
import { Popover, Button, Modal, Input, Tooltip } from 'antd';
import styled from 'styled-components';
import Editor, { EditProps } from './editor';

// const Editor = React.lazy(() => import('./editor'));

export default (props: EditProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  const editor = useMemo(() => <MyEditor full={modalVisible} {...props} />, [
    modalVisible,
    props,
  ]);
  return (
    <div
      style={
        props.disable
          ? { opacity: 0.5, pointerEvents: 'none', background: '#eee' }
          : {}
      }
    >
      <Popover
        content={
          <Button
            type="link"
            icon={<FullscreenExitOutlined />}
            size={'small'}
            onClick={() => setModalVisible(true)}
          >
            全屏
          </Button>
        }
      >
        <div>{!modalVisible ? editor : <Input.TextArea />}</div>
      </Popover>
      <Modal
        destroyOnClose
        visible={modalVisible}
        footer={false}
        width={'98vw'}
        closeIcon={
          <Tooltip title={'退出全屏'}>
            <FullscreenExitOutlined />
          </Tooltip>
        }
        onCancel={() => setModalVisible(false)}
      >
        <br />
        {editor}
      </Modal>
    </div>
  );
};

const MyEditor = styled((props: EditProps) => <Editor {...props} />)<{
  full: boolean;
}>`
  .w-e-text-container {
    height: ${({ full }) => (full ? '90vh' : 300)} !important;
  }
`;
