import React from 'react';
import { Spin } from 'antd';
import { appData } from '../../../app';
import './index.css';
import { default as Wangeditor } from 'wangeditor';
import pasteFromWord from 'paste-from-word';
import xss from 'xss';

const paster = new pasteFromWord();

let imgTags;
let hexImages;
let newSrcValues;

// XSS 配置，允许 style 样式
const xssOptions = {
  whiteList: {
    // 允许 style 标签
    style: ['type'],
    // 允许常见的 HTML 标签及其 style 属性
    div: ['style', 'class', 'id'],
    span: ['style', 'class', 'id'],
    p: ['style', 'class', 'id'],
    h1: ['style', 'class', 'id'],
    h2: ['style', 'class', 'id'],
    h3: ['style', 'class', 'id'],
    h4: ['style', 'class', 'id'],
    h5: ['style', 'class', 'id'],
    h6: ['style', 'class', 'id'],
    strong: ['style', 'class', 'id'],
    b: ['style', 'class', 'id'],
    em: ['style', 'class', 'id'],
    i: ['style', 'class', 'id'],
    u: ['style', 'class', 'id'],
    br: [],
    img: ['src', 'alt', 'style', 'class', 'id', 'width', 'height'],
    a: ['href', 'target', 'style', 'class', 'id'],
    ul: ['style', 'class', 'id'],
    ol: ['style', 'class', 'id'],
    li: ['style', 'class', 'id'],
    table: ['style', 'class', 'id'],
    tr: ['style', 'class', 'id'],
    td: ['style', 'class', 'id'],
    th: ['style', 'class', 'id'],
    thead: ['style', 'class', 'id'],
    tbody: ['style', 'class', 'id'],
    blockquote: ['style', 'class', 'id'],
    pre: ['style', 'class', 'id'],
    code: ['style', 'class', 'id'],
  },
  // 允许 CSS 样式属性
  css: {
    whiteList: {
      'color': true,
      'background-color': true,
      'font-size': true,
      'font-weight': true,
      'font-family': true,
      'text-align': true,
      'text-decoration': true,
      'margin': true,
      'margin-top': true,
      'margin-bottom': true,
      'margin-left': true,
      'margin-right': true,
      'padding': true,
      'padding-top': true,
      'padding-bottom': true,
      'padding-left': true,
      'padding-right': true,
      'border': true,
      'border-color': true,
      'border-width': true,
      'border-style': true,
      'width': true,
      'height': true,
      'line-height': true,
      'display': true,
      'float': true,
      'clear': true,
      'position': true,
      'top': true,
      'bottom': true,
      'left': true,
      'right': true,
      'z-index': true,
    }
  }
};

export interface EditProps {
  value?: string;
  onChange?: (value?: string) => void;
  className?: string;
  customConfig?: any;
  disable?: boolean;
}

export default class Editor extends React.Component<
  EditProps,
  { uploadLoading: boolean }
> {
  // eslint-disable-next-line @typescript-eslint/explicit-member-accessibility
  state = {
    uploadLoading: false,
  };

  private editor: any;
  private value?: string;
  private dom?: HTMLDivElement;

  public render() {
    return (
      <Spin spinning={this.state.uploadLoading} tip={'文件上传中...'}>
        <div
          ref={(dom: any) => (this.dom = dom)}
          style={{ textAlign: 'left' }}
          className={this.props.className}
        />
      </Spin>
    );
  }

  public componentWillReceiveProps({ value }: EditProps) {
    if (this.editor && this.value !== value) {
      this.value = value;
      console.log(this.value);
      if (value) {
        const safeHtml = xss(value, xssOptions);
        this.editor.txt.html(safeHtml);
      } else {
        this.editor.txt.clear();
      }
    }
  }

  public componentDidMount() {
    if (this.dom) {
      const editor = new Wangeditor(this.dom);
      this.editor = editor;
      const { uploadFn } = appData.storeValue;
      Object.assign(editor.config, this.props.customConfig);
      editor.config.uploadVideoServer = '/api/upload-video';
      editor.config.onchange = (html?: string) => {
        if (this.props.onChange) {
          if (html === '<p><br></p>') {
            html = undefined;
          }
          this.value = html;
          this.props.onChange(html);
        }
      };
      if (uploadFn) {
        editor.config.customUploadImg = (files: File[], insert: any) => {
          this.setState({ uploadLoading: true });
          Promise.all(files.map(file => uploadFn(file))).then(res => {
            if (res?.length) {
              res.forEach(item => {
                insert(item);
              });
              this.setState({ uploadLoading: false });
            }
          });
        };
        editor.config.customUploadVideo = (files: File[], insert: any) => {
          this.setState({ uploadLoading: true });
          uploadFn(files[0]).then(str => {
            insert(str);
            this.setState({ uploadLoading: false });
          });
        };
      }

      editor.create();
      const safeHtml = xss(this.props.value, xssOptions);
      editor.txt.html(safeHtml);
      //捕获模式，需要优先于wangeditor的的粘贴事件
      editor.$textElem.elems[0].addEventListener(
        'paste',
        pasteEvent => {
          const mswordHtml = paster.stripHtml(
            paster.getClipboardData(pasteEvent.clipboardData, 'text/html'),
          );
          const rtf = pasteEvent.clipboardData.getData('text/rtf');
          imgTags = paster.extractTagsFromHtml(mswordHtml);
          hexImages = paster.extractFromRtf(rtf);
          newSrcValues = hexImages.map(img => paster.createSrcWithBase64(img));
        },
        true,
      );

      editor.config.pasteTextHandle = pasteStr => {
        if (imgTags) {
          const promiseList = [];
          imgTags.forEach((img, index) => {
            if (!newSrcValues[index]) {
              return;
            }
            const base64Img = newSrcValues[index];
            if (img.indexOf('file://') === 0) {
              const escapedPath = img.replace(/\\/g, '\\\\');
              const imgRegex = new RegExp(
                '(<img [^>]*src=["\']?)' + escapedPath,
              );
              if (uploadFn) {
                promiseList.push(
                  new Promise(resolve => {
                    const file = dataURLtoFile(base64Img, 'png');
                    uploadFn(file)
                      .then(src =>
                        resolve({
                          origin: escapedPath,
                          dest: src,
                        }),
                      )
                      .catch(err => {
                        resolve(undefined);
                      });
                  }),
                );
              } else {
                pasteStr = pasteStr.replace(imgRegex, '$1' + base64Img);
              }
            }
          });
          if (uploadFn) {
            this.setState({ uploadLoading: true });
            Promise.all(promiseList)
              .then(res => {
                res.forEach(imgObj => {
                  if (!imgObj || !imgObj.dest) {
                    return;
                  }
                  pasteStr = pasteStr.replace(imgObj.origin, imgObj.dest);
                });
                editor.cmd.do('insertHTML', pasteStr);
              })
              .finally(() => {
                imgTags = undefined;
                hexImages = undefined;
                newSrcValues = undefined;
                this.setState({ uploadLoading: false });
              });
            return;
          } else {
            return pasteStr;
          }
        }
        return pasteStr;
      };
    }
  }
}

/** base64转file对象 */
function dataURLtoFile(dataurl, filename) {
  const arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}
