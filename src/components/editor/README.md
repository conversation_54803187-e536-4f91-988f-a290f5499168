# 富文本编辑器

### 简单使用

最简单的使用方式。

```tsx
import React from 'react';
import { Editor } from 'parsec-admin';

// const txt = `<h1 style="color: red; text-align: center">测试</h1>`;
const txt = `<div>您即将向<span>药师进行图文咨询（仅提供诊疗建议，不可开具处方），</span><font color=\"#c24f4a\">每次咨询可追问6个问题。</font><span>若药师未在24小时内回复您的咨询，系统将自动关闭本次咨询。</span><span>因药师回复咨询需一定时间，急诊患者请自行前往医院就诊。</span></div>`

export default () => <Editor value={txt} />;
```

### 表单使用

可以像 Input 一样在 formItem 里使用的富文本编辑器。

```tsx
import React from 'react';
import { Editor } from 'parsec-admin';
import { Form, Button } from 'antd';

export default () => (
  <Form>
    <Form.Item
      name={'text'}
      rules={[{ required: true, message: '内容是必填的' }]}
    >
      <Editor />
    </Form.Item>
    <Form.Item>
      <Button type={'primary'} htmlType={'submit'}>
        提交
      </Button>
    </Form.Item>
  </Form>
);
```

## 自定义配置

```tsx
import React from 'react';
import { Editor } from 'parsec-admin';
import { Form, Button } from 'antd';

export default () => (
  <Form>
    <Form.Item
      name={'text'}
      rules={[{ required: true, message: '内容是必填的' }]}
    >
      <Editor
        customConfig={{ menus: ['head', 'bold', 'italic', 'underline'] }}
      />
    </Form.Item>
    <Form.Item>
      <Button type={'primary'} htmlType={'submit'}>
        提交
      </Button>
    </Form.Item>
  </Form>
);
```

## API

> 在 App 里配置了 uploadFn 后可以实现上传。

| 属性         | 说明                                                                              | 默认值 |
| ------------ | --------------------------------------------------------------------------------- | ------ |
| customConfig | 可以根据[文档](https://www.kancloud.cn/wangfupeng/wangeditor3/332599)自定义配置。 | -      |
