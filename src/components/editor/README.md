# 富文本编辑器

### 简单使用

```tsx
import React from 'react';
import { Editor } from 'parsec-admin';

const txt = `<h1 style="color: red; text-align: center">测试</h1>`

export default () => <Editor value={txt} />;
```

### 表单使用

可以像 Input 一样在 formItem 里使用的富文本编辑器。

```tsx
import React from 'react';
import { Editor } from 'parsec-admin';
import { Form, Button } from 'antd';

export default () => (
  <Form>
    <Form.Item
      name={'text'}
      rules={[{ required: true, message: '内容是必填的' }]}
    >
      <Editor />
    </Form.Item>
    <Form.Item>
      <Button type={'primary'} htmlType={'submit'}>
        提交
      </Button>
    </Form.Item>
  </Form>
);
```

## 自定义配置

```tsx
import React from 'react';
import { Editor } from 'parsec-admin';
import { Form, Button } from 'antd';

export default () => (
  <Form>
    <Form.Item
      name={'text'}
      rules={[{ required: true, message: '内容是必填的' }]}
    >
      <Editor
        customConfig={{ menus: ['head', 'bold', 'italic', 'underline'] }}
      />
    </Form.Item>
    <Form.Item>
      <Button type={'primary'} htmlType={'submit'}>
        提交
      </Button>
    </Form.Item>
  </Form>
);
```

## API

> 在 App 里配置了 uploadFn 后可以实现上传。

| 属性         | 说明                                                                              | 默认值 |
| ------------ | --------------------------------------------------------------------------------- | ------ |
| customConfig | 可以根据[文档](https://www.kancloud.cn/wangfupeng/wangeditor3/332599)自定义配置。 | -      |
