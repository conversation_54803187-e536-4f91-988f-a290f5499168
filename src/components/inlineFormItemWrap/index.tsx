import styled from 'styled-components';
import React, { PropsWithChildren } from 'react';
import { getPrefixCls } from '../../_utils';

export default styled(
  ({
    hideRequiredMark,
    ...props
  }: PropsWithChildren<{ className?: string; hideRequiredMark?: boolean }>) => (
    <div {...props} />
  ),
)`
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  .${getPrefixCls}-form-item-explain {
    position: absolute;
    bottom: -24px;
  }
  > * {
    margin-bottom: 0 !important;
    margin-right: 16px !important;
  }
  .${getPrefixCls}-form-item-label
    > label.${getPrefixCls}-form-item-required::before {
    display: ${({ hideRequiredMark }) =>
      hideRequiredMark ? 'none' : 'inline-block'};
  }
`;
