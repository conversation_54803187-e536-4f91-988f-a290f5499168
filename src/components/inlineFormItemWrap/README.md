# 行内表单项

当 Form.Item 里需要包含好几个表单项时可以用这个包裹。

```tsx
import React from 'react';
import { InputNumber } from 'antd';
import { InlineFormItemWrap, Form } from 'parsec-admin';

export default () => (
  <Form
    onSubmit={values => {
      console.log(values);
      return Promise.resolve();
    }}
    items={[
      {
        label: '范围',
        required: true,
        render: (
          <InlineFormItemWrap>
            <Form.Item
              name={'min'}
              rules={[{ required: true, message: '请输入最小值' }]}
            >
              <InputNumber placeholder={'最小值'} />
            </Form.Item>
            <span>至</span>
            <Form.Item
              name={'max'}
              rules={[{ required: true, message: '请输入最大值' }]}
            >
              <InputNumber placeholder={'最大值'} />
            </Form.Item>
          </InlineFormItemWrap>
        ),
      },
      {
        label: '百分比',
        required: true,
        render: (
          <InlineFormItemWrap>
            <Form.Item
              name={'percent'}
              rules={[{ required: true, message: '请输入百分比' }]}
            >
              <InputNumber placeholder={'请输入'} />
            </Form.Item>
            <span>%</span>
          </InlineFormItemWrap>
        ),
      },
    ]}
  />
);
```

## API

| 属性             | 说明                 | 默认值  |
| ---------------- | -------------------- | ------- |
| hideRequiredMark | 隐藏必填项的 \* 号。 | `false` |
