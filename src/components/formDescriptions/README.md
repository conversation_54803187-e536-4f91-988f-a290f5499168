# 表述表单

可以展示详情，也可以展示为表单来提交，一般用在可编辑的详情页，样式来自 [高级表单](https://preview.pro.ant.design/form/advanced-form)

```tsx
/**
 * background: '#f6f7f9'
 */
import React, { useState } from 'react';
import {
  FormDescriptions,
  CardLayout,
  ArrSelect,
  DayRangePicker,
  DateShow,
  Form,
} from 'parsec-admin';
import { Input, Button } from 'antd';
import moment from 'moment';

export default () => {
  const [isEdit, setIsEdit] = useState(true);
  const [data, setData] = useState({
    name: '仓库名',
    domainName: '仓库域名',
    controller: '小吴',
    approver: '小红',
    type: '私密',
  });
  const [form] = Form.useForm();
  return (
    <CardLayout
      title={'仓库管理'}
      extra={
        isEdit ? (
          <Button
            onClick={() =>
              form
                .validateFields()
                .then(values => setData(values))
                .then(() => setIsEdit(false))
            }
          >
            保存
          </Button>
        ) : (
          <Button type={'primary'} onClick={() => setIsEdit(true)}>
            编辑
          </Button>
        )
      }
    >
      <FormDescriptions
        data={data}
        edit={isEdit}
        form={form}
        items={[
          {
            label: '仓库名',
            name: 'name',
            required: true,
            formItemProps: {
              render: <Input placeholder={'请输入仓库名称'} />,
            },
          },
          {
            label: '仓库域名',
            name: 'domainName',
            required: true,
            formItemProps: {
              render: (
                <Input
                  placeholder={'请输入'}
                  addonBefore="http://"
                  addonAfter=".com"
                />
              ),
            },
          },
          {
            label: '仓库管理员',
            name: 'controller',
            required: true,
            formItemProps: {
              render: (
                <ArrSelect
                  options={['小吴', '小红']}
                  placeholder={'请选择仓库管理员'}
                />
              ),
            },
          },
          {
            label: '审批人',
            name: 'approver',
            required: true,
            formItemProps: {
              render: (
                <ArrSelect
                  options={['小吴', '小红']}
                  placeholder={'请选择审批人'}
                />
              ),
            },
          },
          {
            label: '生效日期',
            name: ['startAt', 'endAt'],
            required: true,
            formItemProps: {
              render: <DayRangePicker />,
            },
            render: ([date1, date2] = []) => (
              <>
                <DateShow>{date1}</DateShow> - <DateShow>{date2}</DateShow>
              </>
            ),
          },
          {
            label: '仓库类型',
            name: 'type',
            required: true,
            formItemProps: {
              render: (
                <ArrSelect
                  options={['私密', '公开']}
                  placeholder={'请选择仓库类型'}
                />
              ),
            },
          },
        ]}
      />
    </CardLayout>
  );
};
```

### 表单联动

```tsx
import React from 'react';
import { FormDescriptions } from 'parsec-admin';
import { Switch } from 'antd';

export default () => {
  return (
    <FormDescriptions
      edit
      items={[
        {
          label: '显示input',
          name: 'show',
          valuePropName: 'checked',
          initialValue: false,
          formItemProps: {
            render: <Switch />,
          },
        },
        [({ show }) => ({
          label: '输入框',
          name: 'value',
          hidden: !show,
          /**
           * 传入使用了values里的什么字段可以优化性能
           */
        }), ['show']],
      ]}
    />
  );
};
```

## API

| 属性         | 说明                                                     | 默认值              |
| ------------ | -------------------------------------------------------- | ------------------- |
| form         | 自定义 form 实例。设为 `false` 可以不渲染 form 。        | `form.useForm()[0]` |
| items        | 描述项。                                                 | -                   |
| edit         | 是否可编辑。                                             | `false`             |
| loading      | 是否是加载中。                                           | `false`             |
| data         | 需要显示的详情数据。                                     | -                   |
| autoCacheKey | 自动缓存表单数据，未提交之前结束页面后再进入将回填数据。 | -                   |

> 更多用法可以查看 [Descriptions](https://ant.design/components/descriptions-cn/) 。
