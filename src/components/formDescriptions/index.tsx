import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Descriptions, Spin, Form } from 'antd';
import { DescriptionsProps } from 'antd/lib/descriptions';
import { DescriptionsItemProps } from 'antd/lib/descriptions/Item';
import styled, { css } from 'styled-components';
import { FormInstance } from 'antd/lib/form/hooks/useForm';
import { Store } from 'rc-field-form/lib/interface';
import classnames from 'classnames';
import MyForm, { MyFormItem, MyFormItemObjProps } from '../form';
import { FormProps } from 'antd/lib/form';
import { getPrefixCls } from '../../_utils';
import { FormInstance as RCFormInstance } from 'rc-field-form';
import { useForceUpdate, useRefState } from 'parsec-hooks';
const CircularJSON = require('circular-json');

type MyDescriptionsItemProps<D> = {
  layout?: 'horizontal' | 'vertical';
  hidden?: boolean;
  formItemProps?: {
    render?: MyFormItemObjProps<D>['render'];
  };
  /**
   * 单独设置编辑
   */
  edit?: boolean;
  children?: React.ReactNode;
} & MyFormItemObjProps<D> &
  Omit<DescriptionsItemProps, 'children'>;

type ItemPropsRender<D> = (
  values: D | undefined,
  formInstance: RCFormInstance,
) => MyDescriptionsItemProps<D>;

export interface FormDescriptionsProps<D = Store> extends DescriptionsProps {
  data?: D;
  edit?: boolean;
  form?: FormInstance | false;
  formProps?: FormProps;
  loading?: boolean;
  items: (
    | MyDescriptionsItemProps<D>
    | ItemPropsRender<D>
    | [
        ItemPropsRender<D>,
        /**
         * 传入使用了values里的什么字段可以优化性能
         */
        string[],
      ]
  )[];
  /**
   * 自动缓存表单数据，未提交之前结束页面后再进入将回填数据
   */
  autoCacheKey?: string;
}

export default function<D>({
  data: outData,
  edit,
  items,
  form = MyForm.useForm()[0],
  loading = false,
  formProps,
  autoCacheKey,
  ...descriptionsProps
}: FormDescriptionsProps<D>) {
  const data = useMemo(() => ({ ...outData }), [outData]);
  const arrayNameItems = useMemo(
    () =>
      items
        .filter(({ name }: any) => name instanceof Array)
        .map(({ name }: any) => name as string[]),
    [items],
  );
  const transformValues = useCallback(
    ({ ...values }: any) => {
      if (values) {
        arrayNameItems.forEach(names => {
          if (names.some(n => n in values)) {
            values[names.join()] = names.map(n => values[n]);
          }
        });
      }
      return values;
    },
    [arrayNameItems],
  );
  useEffect(() => {
    if (form) {
      const oldForm = { ...form };
      form.getFieldsValue = ((nameList, filterFunc) => {
        const values = oldForm.getFieldsValue(nameList, filterFunc);
        arrayNameItems.forEach(names => {
          const valueName = names.join();
          names.forEach((n, i) => (values[n] = (values[valueName] || [])[i]));
        });
        return values;
      }) as any;
      form.validateFields = (...arg) => {
        return oldForm.validateFields(...arg).then(values => {
          arrayNameItems.forEach(names => {
            const namesStr = names.join();
            names.forEach(
              (n, i) => (values[n] = (values[namesStr] || [])[i] || values[n]),
            );
            delete values[namesStr];
          });
          return values;
        });
      };
      form.setFieldsValue = values => {
        values = transformValues(values);
        return oldForm.setFieldsValue(values);
      };
    }
  }, [transformValues, arrayNameItems, form]);
  const preData = useRef<D>();
  useEffect(() => {
    if (
      CircularJSON.stringify(preData.current) !== CircularJSON.stringify(data)
    ) {
      preData.current = { ...data };
      form && form.setFieldsValue(data);
    }
  }, [data, preData, form]);
  const hideKeysRef = useRef<number[]>([]);
  const { forceUpdate, updateTime } = useForceUpdate();
  const [propsArr, setPropsArr, propsArrRef] = useRefState<{
    [index: number]: any;
  }>({});
  const renderItems = useCallback(
    (arr: typeof items, setHide = false) => {
      return arr
        .filter(({ hidden }: any) => !hidden)
        .map((props2, index) => {
          const renderItem = ({
            layout,
            render,
            children,
            hidden,
            className,
            ...props
          }: MyDescriptionsItemProps<D>) => {
            let { name } = props;
            name = name instanceof Array ? name.join() : name || '';
            const values = data ? transformValues(data) : data;
            const result =
              values && (!name || values[name] !== undefined || render)
                ? render
                  ? render instanceof Function
                    ? render(values[name], values)
                    : render
                  : values[name]
                : undefined;
            const { label, formItemProps, span, style, edit: itemEdit } = props;
            propsArrRef.current = { ...propsArrRef.current };
            propsArrRef.current[index] = { span, style, layout, render, label };
            const Null = () => {
              useEffect(() => {
                const hideKeys = hideKeysRef.current;
                if (
                  (hidden && !hideKeys.includes(index)) ||
                  (!hidden && hideKeys.includes(index))
                ) {
                  if (hideKeys.includes(index)) {
                    hideKeys.splice(hideKeys.indexOf(index), 1);
                  } else {
                    hideKeys.push(index);
                  }
                  forceUpdate();
                }
                if (
                  CircularJSON.stringify(propsArrRef.current) !==
                  CircularJSON.stringify(propsArr)
                ) {
                  setPropsArr({ ...propsArrRef.current });
                }
              }, []);
              return null;
            };
            return (
              <Descriptions.Item
                key={name + index}
                className={classnames(layout, className)}
                {...props}
                labelStyle={
                  !edit && itemEdit
                    ? { alignItems: 'center' }
                    : props.labelStyle
                }
                label={edit ? undefined : label}
              >
                {setHide && <Null />}
                {edit || itemEdit ? (
                  <FormItem
                    render={formItemProps?.render}
                    {...props}
                    hideLabel={!edit && itemEdit}
                    style={
                      !edit && itemEdit
                        ? { marginBottom: 0, paddingLeft: 0 }
                        : props.style
                    }
                  />
                ) : children ? (
                  children
                ) : [undefined, null].includes(result) ? (
                  '-'
                ) : (
                  result
                )}
              </Descriptions.Item>
            );
          };
          return props2 instanceof Function || props2 instanceof Array ? (
            <div
              key={index}
              {...propsArrRef.current[index]}
              {...(edit ? { label: undefined } : {})}
            >
              <Form.Item
                shouldUpdate={
                  props2[1]
                    ? (v1, v2) => {
                        return props2[1].some(key => v1[key] !== v2[key]);
                      }
                    : true
                }
                noStyle
              >
                {instance =>
                  renderItem(
                    (props2 instanceof Array ? props2[0] : (props2 as any))(
                      { ...data, ...instance.getFieldsValue() },
                      instance,
                    ),
                  )
                }
              </Form.Item>
            </div>
          ) : (
            !setHide && renderItem(props2 as any)
          );
        });
    },
    [
      propsArrRef,
      data,
      transformValues,
      edit,
      propsArr,
      forceUpdate,
      setPropsArr,
    ],
  );
  const children = useMemo(() => {
    return (
      <>
        <div style={{ display: 'none' }}>
          {items
            .filter(({ hidden }: any) => hidden)
            .map(({ name }: any, index) => (
              <FormItem key={name + index} name={name} />
            ))}
          {renderItems(items, true)}
        </div>
        <MyDescriptions
          hideRightPadding={formProps?.layout !== 'vertical'}
          className={classnames(descriptionsProps.className)}
          edit={edit}
          {...descriptionsProps}
          bordered={edit ? false : descriptionsProps.bordered}
        >
          {renderItems(items).filter(
            (_, index) => updateTime && !hideKeysRef.current.includes(index),
          )}
        </MyDescriptions>
      </>
    );
  }, [
    hideKeysRef,
    renderItems,
    items,
    formProps,
    descriptionsProps,
    edit,
    updateTime,
  ]);
  return (
    <Spin spinning={loading}>
      {form ? (
        <MyForm
          form={form}
          submitButton={false}
          layout={false}
          formProps={{
            requiredMark: 'optional',
            ...formProps,
            layout: formProps?.layout || 'vertical',
          }}
          autoCacheKey={autoCacheKey}
        >
          {children}
        </MyForm>
      ) : (
        children
      )}
    </Spin>
  );
}

const FormItem = styled(MyFormItem)<{ hideLabel?: boolean; style?: any }>`
  &&& {
    &.${getPrefixCls}-form {
      &-item {
        padding: 0 8px;
        padding-right: 20%;
        width: 100%;
        .ant-col:first-child {
          ${({ hideLabel }) =>
            hideLabel &&
            css`
              display: none;
            `}
        }
      }
    }
  }
`;

const MyDescriptions = styled(
  ({
    edit,
    hideRightPadding,
    ...props
  }: DescriptionsProps & { edit?: boolean; hideRightPadding?: boolean }) => (
    <Descriptions {...props} />
  ),
)`
  margin: ${({ edit }) => (edit ? '0 -8px' : 0)};
  .${getPrefixCls}-descriptions {
    &-item {
      width: 100%;
      padding-right: ${({ edit, hideRightPadding }) =>
        edit && !hideRightPadding ? 24 : 0}px;
      padding-bottom: ${({ edit }) => (edit ? 0 : 16)}px;
      &.vertical {
        display: flex;
        flex-direction: column;
      }
      &-content {
        width: ${({ edit }) => (edit ? '100%' : '')};
        display: ${({ layout }) => (layout === 'vertical' ? '' : 'block')};
      }
      &:nth-child(${({ column = 3 }) => column}n),
      &:last-child {
        .${getPrefixCls}-form-item {
          padding-right: 0;
        }
      }
      @media (max-width: 1200px) {
        .${getPrefixCls}-form-item {
          padding-right: 0;
        }
      }
    }
  }
`;
