# 多选框

根据 Options 可以创建一个 CheckboxGroup。

```tsx
import React from 'react';
import { CheckboxGroup, Form } from 'parsec-admin';

export default () => (
  <Form
    items={[
      {
        label: '多选框',
        name: 'check',
        required: true,
        render: (
          <CheckboxGroup
            options={[
              { label: 1, value: 1 },
              { label: 2, value: 2 },
            ]}
          />
        ),
      },
    ]}
  />
);
```

## API

| 属性     | 说明                             | 默认值  |
| -------- | -------------------------------- | ------- |
| options  | 需要渲染的 Checkbox 数组。       | -       |
| objValue | 是否需要转成 `Object` 类型的值。 | `false` |
