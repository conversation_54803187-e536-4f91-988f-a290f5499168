import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Checkbox, Divider } from 'antd';

const CheckboxGroup = Checkbox.Group;

type Value = (string | number | boolean)[] | {};

interface Props {
  value?: Value;
  onChange?: (value?: Value) => void;
  options: {
    label: string;
    value: string | number | boolean;
  }[];
  objValue?: boolean;
  hideCheckAll?: boolean;
  disabled?: boolean;
}

export default ({
  options,
  value,
  onChange,
  objValue,
  disabled,
  hideCheckAll,
}: Props) => {
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const arrValue: any[] = useMemo(
    () =>
      value instanceof Array
        ? value
        : value
        ? options
            .filter(({ value: iv }) =>
              // eslint-disable-next-line eqeqeq
              Object.keys(value).some(v => v == iv && value[v]),
            )
            .map(({ value }) => value)
        : [],
    [options, value],
  );
  useEffect(() => {
    setIndeterminate(
      !!arrValue && arrValue.length !== options.length && arrValue.length !== 0,
    );
    setCheckAll(!!arrValue && arrValue.length === options.length);
  }, [options.length, arrValue]);
  const handleChange = useCallback(
    (value: Value) => {
      if (objValue && value instanceof Array) {
        const obj: any = {};
        value.forEach((k: string) => (obj[k] = true));
        value = Object.keys(obj).length ? obj : undefined;
        if (value) {
          options.forEach(({ value: k }) => {
            if (!value[k as any]) {
              value[k as any] = false;
            }
          });
        }
      }
      onChange && onChange(value);
    },
    [options, onChange, objValue],
  );
  return (
    <>
      {!hideCheckAll && (
        <>
          <Checkbox
            disabled={disabled}
            indeterminate={indeterminate}
            onChange={({ target: { checked } }) => {
              setCheckAll(checked);
              handleChange(checked ? options.map(({ value }) => value) : []);
            }}
            checked={checkAll}
          >
            选择全部
          </Checkbox>
          <Divider style={{ margin: '0 0 10px' }} />
        </>
      )}
      <CheckboxGroup
        disabled={disabled}
        value={arrValue}
        options={options}
        onChange={values => {
          handleChange(values);
          setCheckAll(values.length === options.length);
          setIndeterminate(!!values.length && !checkAll);
        }}
      />
    </>
  );
};
