# 海报编辑器

用于输入以分为单位的输入框，以分为单位传入，会以元显示，提交则又会转换为以分单位。

```tsx
import React from 'react';
import { PostEditor, useModal, PortalProvider } from 'parsec-admin';
import {Button} from 'antd'

const Demo = () => {
  const showModal = useModal({children: <PostEditor/>, width: '90vw'});
  return (
    <Button onClick={() => showModal()}>
      显示
    </Button>
  );
};

export default () => <PortalProvider><Demo/></PortalProvider>
```

## API

| 属性 | 说明                   | 默认值 |
| ---- | ---------------------- | ------ |
| unit | input 后面显示的单位。 | -      |

> 更多使用方法查看 [InputNumber](https://ant.design/components/input-number-cn/#API) 。
