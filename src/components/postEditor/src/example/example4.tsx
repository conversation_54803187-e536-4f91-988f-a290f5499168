export default {
  src: require('./example4.jpg'),
  json: {
    version: '3.4.0',
    objects: [
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 640,
        width: 720,
        height: 1280,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 720,
            height: 1280,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 720,
              height: 1280,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp4.jpeg'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 720,
            height: 1280,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp4.jpeg'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 357,
        top: 346,
        width: 254,
        height: 308,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 254,
            height: 308,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 254,
              height: 308,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp5.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 254,
            height: 308,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp5.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 356.5,
        top: 641.51,
        width: 475,
        height: 75.02,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 475,
            height: 75.02,
            fill: '',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 475,
            height: 61.02,
            fill: '#000000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text:
              '努力做一个善良的人，做一个心态阳光的人，做一个积极向上的人，用正能量激发自己',
            fontSize: 20,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.7,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText:
          '努力做一个善良的人，做一个心态阳光的人，做一个积极向上的人，用正能量激发自己',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 727.34,
        width: 600,
        height: 58.68,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 600,
            height: 58.68,
            fill: '',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 600,
            height: 40.68,
            fill: '#000000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '微笑开始新的一天',
            fontSize: 36,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.5,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '微笑开始新的一天',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 595.5,
        top: 1110.5,
        width: 127,
        height: 127,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 1200,
            height: 1200,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 0.11,
            scaleY: 0.11,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 127,
              height: 127,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 9.45,
              scaleY: 9.45,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp6.jpeg'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 127,
            height: 127,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp6.jpeg'),
        rx: 0,
        oldScaleX: 0.10583333333333333,
        oldScaleY: 0.10583333333333333,
        myshadow: '',
      },
    ],
    background: '#f8f8f8',
    canvas: { width: '720', height: '1280' },
  },
};
