export default {
  src: require('./example3.jpg'),
  json: {
    version: '3.4.0',
    objects: [
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 640,
        width: 720,
        height: 1280,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 720,
            height: 1280,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 720,
              height: 1280,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp1.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 720,
            height: 1280,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp1.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 640,
        width: 720,
        height: 1280,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 720,
            height: 1280,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 720,
              height: 1280,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp2.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 720,
            height: 1280,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp2.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 633.5,
        width: 496,
        height: 1013,
        fill: '#ffffff',
        stroke: '#ff0000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: null,
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 496,
            height: 1013,
            fill: '#ffffff',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: null,
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 496,
            height: 1013,
            fill: 'rgba(0,0,0,0)',
            stroke: '#ff0000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'rect',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 362.5,
        top: 441.5,
        width: 363,
        height: 363,
        fill: 'rgba(0,0,0,0)',
        stroke: 'rgba(243,95,131,1)',
        strokeWidth: 2,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: null,
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 361,
            height: 361,
            fill: 'rgba(0,0,0,0)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: null,
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 184.5,
            ry: 184.5,
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 363,
            height: 363,
            fill: 'rgba(0,0,0,0)',
            stroke: 'rgba(243,95,131,1)',
            strokeWidth: 2,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 185.5,
            ry: 185.5,
          },
        ],
        mytype: 'rect',
        rx: 185.5,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 473.07,
        width: 238,
        height: 140.14,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 238,
            height: 140.14,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -14.7,
            width: 238,
            height: 110.74,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '上新',
            fontSize: 98,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '上新',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 361,
        top: 362.07,
        width: 238,
        height: 140.14,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 238,
            height: 140.14,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -14.7,
            width: 238,
            height: 110.74,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '国庆',
            fontSize: 98,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '国庆',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 360,
        top: 201.81,
        width: 120,
        height: 65.62,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 120,
            height: 65.62,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -13.6,
            width: 120,
            height: 38.42,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: 'LOGO',
            fontSize: 34,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.8,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: 'LOGO',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 355.5,
        top: 565,
        width: 319,
        height: 58,
        fill: 'rgba(243,95,131,1)',
        stroke: '#ff0000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: null,
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 319,
            height: 58,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: null,
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 29,
            ry: 29,
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 319,
            height: 58,
            fill: 'rgba(0,0,0,0)',
            stroke: '#ff0000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 29,
            ry: 29,
          },
        ],
        mytype: 'rect',
        rx: 29,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 178,
        top: 426.5,
        width: 128,
        height: 95,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 128,
            height: 95,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 128,
              height: 95,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp3.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 128,
            height: 95,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp3.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 522.5,
        top: 358.5,
        width: 91,
        height: 125,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 91,
            height: 125,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 91,
              height: 125,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp4.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 91,
            height: 125,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp4.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 532,
        top: 464.5,
        width: 128,
        height: 95,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 128,
            height: 95,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 128,
              height: 95,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp5.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 128,
            height: 95,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp5.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 359.5,
        top: 678.99,
        width: 459,
        height: 91.99,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 459,
            height: 91.99,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -7.8,
            width: 459,
            height: 76.39,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '前100位消费者更有好礼相送！\n 数量有限，速来抢购！',
            fontSize: 26,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.6,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '前100位消费者更有好礼相送！\n 数量有限，速来抢购！',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 359,
        top: 783.07,
        width: 372,
        height: 106.14,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 372,
            height: 106.14,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -9,
            width: 372,
            height: 88.14,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '全场满500减200\n满800减350!',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.6,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'center',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '全场满500减200\n满800减350!',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 359,
        top: 859.95,
        width: 358,
        height: 51.9,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 358,
            height: 51.9,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -9,
            width: 358,
            height: 33.9,
            fill: 'rgba(243,95,131,1)',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '活动时间：10.01-10.15',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.6,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '活动时间：10.01-10.15',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 372,
        top: 995,
        width: 148,
        height: 148,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 500,
            height: 500,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 0.3,
            scaleY: 0.3,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 148,
              height: 148,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 3.38,
              scaleY: 3.38,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp6.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 148,
            height: 148,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp6.png'),
        rx: 0,
        oldScaleX: 0.296,
        oldScaleY: 0.296,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 627,
        top: 518.5,
        width: 186,
        height: 377,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 186,
            height: 377,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 186,
              height: 377,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1,
              scaleY: 1,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 0,
              ry: 0,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp7.png'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 186,
            height: 377,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp7.png'),
        rx: 0,
        oldScaleX: 1,
        oldScaleY: 1,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 387,
        top: 565.45,
        width: 309,
        height: 42.9,
        fill: 'rgba(0,0,0,0)',
        stroke: 'rgba(243,95,131,1)',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 309,
            height: 42.9,
            fill: 'rgba(0,0,0,0)',
            stroke: 'rgba(243,95,131,1)',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: -4.5,
            width: 309,
            height: 33.9,
            fill: '#fff',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '新品上市限时折扣',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '新品上市限时折扣',
        rx: 0,
        myshadow: '',
      },
    ],
    background: 'rgba(237,237,237, 1)',
    canvas: { width: '720', height: '1280' },
  },
};
