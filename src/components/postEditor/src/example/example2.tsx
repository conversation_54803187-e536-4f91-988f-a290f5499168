export default {
  src: require('./example2.jpg'),
  json: {
    version: '3.4.0',
    objects: [
      {
        type: 'image',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 101,
        top: 479,
        width: 200,
        height: 200,
        fill: 'rgb(0,0,0)',
        stroke: null,
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: null,
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        clipPath: {
          type: 'rect',
          version: '3.4.0',
          originX: 'center',
          originY: 'center',
          left: 0,
          top: 0,
          width: 200,
          height: 200,
          fill: 'rgb(0,0,0)',
          stroke: null,
          strokeWidth: 1,
          strokeDashArray: null,
          strokeLineCap: 'butt',
          strokeDashOffset: 0,
          strokeLineJoin: 'miter',
          strokeMiterLimit: 4,
          scaleX: 1,
          scaleY: 1,
          angle: 0,
          flipX: false,
          flipY: false,
          opacity: 1,
          shadow: null,
          visible: true,
          clipTo: null,
          backgroundColor: '',
          fillRule: 'nonzero',
          paintFirst: 'fill',
          globalCompositeOperation: 'source-over',
          transformMatrix: null,
          skewX: 0,
          skewY: 0,
          rx: '10',
          ry: '10',
          inverted: false,
          absolutePositioned: false,
        },
        crossOrigin: '',
        cropX: 0,
        cropY: 0,
        src:
          'data:image/png;base64,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',
        filters: [],
        mytype: 'qrcode',
        url: '哈哈哈',
        color: '#000000',
        background: '#ffffff',
        rx: 10,
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 322,
        top: 663,
        width: 100,
        height: 100,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 10,
          offsetX: 10,
          offsetY: 10,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 400,
            height: 400,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 0.25,
            scaleY: 0.25,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 10,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 100,
              height: 100,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 4,
              scaleY: 4,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 50,
              ry: 50,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp3.jpeg'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 100,
            height: 100,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 10,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 50,
            ry: 50,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp3.jpeg'),
        rx: 50,
        oldScaleX: 0.25,
        oldScaleY: 0.25,
        myshadow: '10 10 10 #888888',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 327,
        top: 150,
        width: 654,
        height: 300,
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 10,
          offsetX: 10,
          offsetY: 10,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'image',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 960,
            height: 600,
            fill: 'rgb(0,0,0)',
            stroke: null,
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 0.68,
            scaleY: 0.5,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 10,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            clipPath: {
              type: 'rect',
              version: '3.4.0',
              originX: 'center',
              originY: 'center',
              left: 0,
              top: 0,
              width: 654,
              height: 300,
              fill: 'rgb(0,0,0)',
              stroke: null,
              strokeWidth: 1,
              strokeDashArray: null,
              strokeLineCap: 'butt',
              strokeDashOffset: 0,
              strokeLineJoin: 'miter',
              strokeMiterLimit: 4,
              scaleX: 1.47,
              scaleY: 2,
              angle: 0,
              flipX: false,
              flipY: false,
              opacity: 1,
              shadow: null,
              visible: true,
              clipTo: null,
              backgroundColor: '',
              fillRule: 'nonzero',
              paintFirst: 'fill',
              globalCompositeOperation: 'source-over',
              transformMatrix: null,
              skewX: 0,
              skewY: 0,
              rx: 10,
              ry: 10,
              inverted: false,
              absolutePositioned: false,
            },
            crossOrigin: '',
            cropX: 0,
            cropY: 0,
            src: require('./temp1.jpeg'),
            filters: [],
          },
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 654,
            height: 300,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 10,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 10,
            ry: 10,
          },
        ],
        mytype: 'image',
        mode: 'scaleToFill',
        url: require('./temp2.jpeg'),
        rx: 10,
        oldScaleX: 0.68125,
        oldScaleY: 0.5,
        myshadow: '10 10 10 #888888',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 483.75,
        top: 280.97,
        width: 334.5,
        height: 23.94,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: '',
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 334.5,
            height: 23.94,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 334.5,
            height: 20.34,
            fill: '#f5f5f5',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: '',
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: 'Do not fall in love with people like me',
            fontSize: 18,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.2,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 4,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: 'Do not fall in love with people like me',
        rx: 0,
        myshadow: '',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 466,
        top: 470.59,
        width: 30,
        height: 219.18,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 5,
          offsetX: 10,
          offsetY: 10,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 219.18,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 210.18,
            fill: '#ff0000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '粒粒皆辛苦',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 6,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '粒粒皆辛苦',
        rx: 0,
        myshadow: '10 10 5 #888888',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 515,
        top: 470.59,
        width: 30,
        height: 219.18,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 5,
          offsetX: 10,
          offsetY: 10,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 219.18,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 210.18,
            fill: '#ff0000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '谁知盘中餐',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 6,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '谁知盘中餐',
        rx: 0,
        myshadow: '10 10 5 #888888',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 565,
        top: 470.59,
        width: 30,
        height: 219.18,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 5,
          offsetX: 10,
          offsetY: 10,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 219.18,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 210.18,
            fill: '#ff0000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '汗滴禾下土',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 6,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '汗滴禾下土',
        rx: 0,
        myshadow: '10 10 5 #888888',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 615,
        top: 470.59,
        width: 30,
        height: 219.18,
        fill: '',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 5,
          offsetX: 10,
          offsetY: 10,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 219.18,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 30,
            height: 210.18,
            fill: '#ff0000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 10,
              offsetY: 10,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '锄禾日当午',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 6,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '锄禾日当午',
        rx: 0,
        myshadow: '10 10 5 #888888',
      },
      {
        type: 'group',
        version: '3.4.0',
        originX: 'center',
        originY: 'center',
        left: 379,
        top: 741.45,
        width: 200,
        height: 42.9,
        fill: 'rgba(0,0,0,0)',
        stroke: '#000000',
        strokeWidth: 0,
        strokeDashArray: null,
        strokeLineCap: 'butt',
        strokeDashOffset: 0,
        strokeLineJoin: 'miter',
        strokeMiterLimit: 4,
        scaleX: 1,
        scaleY: 1,
        angle: 0,
        flipX: false,
        flipY: false,
        opacity: 1,
        shadow: {
          color: '#888888',
          blur: 5,
          offsetX: 3,
          offsetY: 3,
          affectStroke: false,
          nonScaling: false,
        },
        visible: true,
        clipTo: null,
        backgroundColor: '',
        fillRule: 'nonzero',
        paintFirst: 'fill',
        globalCompositeOperation: 'source-over',
        transformMatrix: null,
        skewX: 0,
        skewY: 0,
        objects: [
          {
            type: 'rect',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 200,
            height: 42.9,
            fill: 'rgba(0,0,0,0)',
            stroke: '#000000',
            strokeWidth: 0,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 3,
              offsetY: 3,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            rx: 0,
            ry: 0,
          },
          {
            type: 'textbox',
            version: '3.4.0',
            originX: 'center',
            originY: 'center',
            left: 0,
            top: 0,
            width: 200,
            height: 33.9,
            fill: '#000000',
            stroke: null,
            strokeWidth: 1,
            strokeDashArray: null,
            strokeLineCap: 'butt',
            strokeDashOffset: 0,
            strokeLineJoin: 'miter',
            strokeMiterLimit: 4,
            scaleX: 1,
            scaleY: 1,
            angle: 0,
            flipX: false,
            flipY: false,
            opacity: 1,
            shadow: {
              color: '#888888',
              blur: 5,
              offsetX: 3,
              offsetY: 3,
              affectStroke: false,
              nonScaling: false,
            },
            visible: true,
            clipTo: null,
            backgroundColor: '',
            fillRule: 'nonzero',
            paintFirst: 'fill',
            globalCompositeOperation: 'source-over',
            transformMatrix: null,
            skewX: 0,
            skewY: 0,
            text: '玲小一',
            fontSize: 30,
            fontWeight: 'normal',
            fontFamily: '',
            fontStyle: 'normal',
            lineHeight: 1.3,
            underline: false,
            overline: false,
            linethrough: false,
            textAlign: 'left',
            textBackgroundColor: '',
            charSpacing: 0,
            minWidth: 20,
            splitByGrapheme: true,
            styles: {},
            maxLines: 2,
            textDecoration: 'none',
            textStyle: 'fill',
          },
        ],
        mytype: 'textGroup',
        oldText: '玲小一',
        rx: 0,
        myshadow: '3 3 5 #888888',
      },
    ],
    background: '#fff',
  },
};
