@font-face {
  font-family: 'webfontzk';
  font-display: swap;
  src: url('//at.alicdn.com/t/webfont_3u4i2u4vd8t.eot'); /* IE9*/
  src: url('//at.alicdn.com/t/webfont_3u4i2u4vd8t.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('//at.alicdn.com/t/webfont_3u4i2u4vd8t.woff2') format('woff2'),
    url('//at.alicdn.com/t/webfont_3u4i2u4vd8t.woff') format('woff'),
    /* chrome、firefox */ url('//at.alicdn.com/t/webfont_3u4i2u4vd8t.ttf') format('truetype'),
    /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/ url('//at.alicdn.com/t/webfont_3u4i2u4vd8t.svg#站酷高端黑')
      format('svg'); /* iOS 4.1- */
}
@font-face {
  font-family: 'webfontzkxw';
  font-display: swap;
  src: url('//at.alicdn.com/t/webfont_blv6u6u65b.eot'); /* IE9*/
  src: url('//at.alicdn.com/t/webfont_blv6u6u65b.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('//at.alicdn.com/t/webfont_blv6u6u65b.woff2') format('woff2'),
    url('//at.alicdn.com/t/webfont_blv6u6u65b.woff') format('woff'),
    /* chrome、firefox */ url('//at.alicdn.com/t/webfont_blv6u6u65b.ttf') format('truetype'),
    /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/ url('//at.alicdn.com/t/webfont_blv6u6u65b.svg#站酷小薇体')
      format('svg'); /* iOS 4.1- */
}

#post-editor {
  display: flex;
  justify-content: flex-end;
  //font-family: 'xingyan';
  .slide {
    flex: 1;
  }
  .placeholder {
    width: auto;
    flex: 1;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    line-height: 25px;
  }

  th {
    background-color: #eee;
  }

  td,
  th {
    text-align: center;
  }

  td:first-child {
    text-align: left;
  }

  #merge {
    width: 100%;
  }
  .box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
    .btns {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .btn {
      margin-left: 10px;
    }
    div {
      margin-left: 10px;
    }
  }
  /*  .main-container {
    background: #f8f8f8;
  } */
  .example {
    display: flex;
    flex-direction: column;
    overflow: auto;
    min-height: 300px;
    background: #ffffff;
    //border-right: 1px solid #eee;
    margin-right: 10px;
    .example-header {
      position: relative;
      padding: 16px 24px;
      color: rgba(0, 0, 0, 0.65);
      //background: #fff;
      border-bottom: 1px solid #e8e8e8;
      border-radius: 4px 4px 0 0;
      .example-header-h3 {
        margin: 0;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
      }
    }
    .ul {
      display: flex;
      justify-content: space-around;
      flex-wrap: wrap;
    }
    .li {
      width: 200px;
      height: 306px;
      margin-top: 10px;
      cursor: pointer;
      border: 1px solid #f0f0f0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .option {
    margin-top: 10px;
    .option-li {
      margin-top: 10px;
      border: 1px solid #eee;
      padding: 0 20px 10px;
      margin-left: 10px;
    }
    .row {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: 10px;
      .h3 {
        color: #333;
        font-size: 16px;
        width: 110px;
        margin-right: 10px;
      }
      .ant-btn {
        margin-right: 10px;
      }
      .ant-input {
        width: 200px;
        min-width: auto;
      }
    }
  }
  .option-drawer {
    /* .option-drawer-header {
      height: 33px;
    } */
    .option-li {
      border: 0;
      padding: 0;
      .row:first-child {
        margin-bottom: 30px;
      }
    }
  }
}
