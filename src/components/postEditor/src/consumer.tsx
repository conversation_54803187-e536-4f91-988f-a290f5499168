import React from 'react';
import { ThemeContext } from './providers';

export default class Consumer extends React.Component {
  render() {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const { children } = this.props;

    return (
      <ThemeContext.Consumer>
        {({ allFood, searchTerm, searchTermChanged }: any) => {
          const food = searchTerm
            ? allFood.filter(
                food =>
                  food.name.toLowerCase().indexOf(searchTerm.toLowerCase()) >
                  -1,
              )
            : allFood;
          console.log('React.Children', React.Children);
          return React.Children.map(children, child =>
            React.cloneElement(child as any, {
              food,
              searchTerm,
              searchTermChanged,
            }),
          );
        }}
      </ThemeContext.Consumer>
    );
  }
}
