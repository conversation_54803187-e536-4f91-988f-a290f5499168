import React, { useEffect, useRef, useState } from 'react';
import { Upload, message } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { appData } from '../../app';
import { UploadFile } from 'antd/lib/upload/interface';

type Value = string[];

export interface MyUploadProps extends Omit<UploadProps, 'onChange'> {
  /**
   * 允许上传的文件数量
   */
  length?: number;
  children?: React.ReactNode;
  value?: Value;
  onChange?: (value?: Value) => void;
  /**
   * 渲染文件名
   * @param url
   */
  renderFileName?: (url: string) => string;
  /**
   * 上传文件最大大小
   * 计算方式 MB * 1024 * 1024 如：1M 写做：1 * 1024 * 1024
   */
  maxSize?: number;
  /**
   * 超出限制大小事件
   */
  onMaxError?: () => void;
  /**
   * 当length===1时生效，是否返回数组value，默认true
   */
  arrValue?: boolean;
  /**
   * 当fileList改变时会执行
   */
  onFileListChange?: (fileList: UploadFile[]) => void;
  /**
   * file类型的值，不再通过上传方法进行上传
   */
  fileValue?: boolean;
}

export default React.memo(
  ({
    length = 1,
    accept = 'image/*',
    children,
    value,
    onChange = () => {},
    beforeUpload = () => true,
    maxSize,
    renderFileName = url => url,
    onMaxError = () => message.error('上传文件过大'),
    onFileListChange,
    arrValue = true,
    fileValue,
    ...otherProps
  }: MyUploadProps) => {
    value = value ? value : undefined;
    value = typeof value === 'string' ? [value] : value;
    const { uploadFn, storage, uploadWithToken = true } = appData.storeValue;
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const preValue = useRef<string[] | undefined>();
    useEffect(() => {
      if (JSON.stringify(value) !== JSON.stringify(preValue.current)) {
        setFileList(
          (value || []).map(url => ({
            url,
            uid: url,
            name: url,
          })) as any,
        );
        preValue.current = value;
      }
    }, [value]);
    return (
      <Upload
        accept={accept}
        customRequest={
          uploadFn
            ? async ({ file, onSuccess, onError }: any) => {
                const maxErrFn = () => maxSize && file.size > maxSize;
                try {
                  if (await maxErrFn()) {
                    if (onMaxError) {
                      onMaxError();
                    }
                    onError('beforeUpload');
                    return;
                  }
                  await beforeUpload(file, fileList as any);
                  if (fileValue) {
                    onSuccess({ url: file });
                  } else {
                    uploadFn(file)
                      .then(url => onSuccess({ url }))
                      .catch(onError);
                  }
                } catch (e) {
                  console.error(e);
                  onError('beforeUpload');
                }
              }
            : undefined
        }
        headers={
          uploadWithToken && storage
            ? {
                Authorization: `Bearer ${storage.get('token')}`.replace(
                  /"/g,
                  '',
                ),
              }
            : undefined
        }
        onChange={({ fileList }) => {
          fileList = fileList.filter(({ error }) => error !== 'beforeUpload');
          if (fileList?.length && fileList?.length > length) {
            fileList?.splice(0, fileList?.length - length);
          }
          const urls = fileList
            .filter(file => {
              const { response, url } = file;
              return !!url || !!response;
            })
            .map(({ url: url1, response: { url } = {} }) => url1 || url);
          onFileListChange?.(fileList);
          setFileList(fileList);
          const value = urls.length ? urls : undefined;
          preValue.current = value;
          onChange(value?.length === 1 ? (arrValue ? value : value[0]) : value);
        }}
        multiple={length > 1}
        name={'file'}
        fileList={fileList.map(obj => ({
          ...obj,
          name: obj.url ? renderFileName(obj.url) : obj.name,
        }))}
        {...otherProps}
      >
        {children}
      </Upload>
    );
  },
  ({ value }, { value: v2 }) => JSON.stringify(value) === JSON.stringify(v2),
);
