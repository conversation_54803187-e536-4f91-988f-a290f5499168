# 上传

可以直接在 FormItem 里使用的上传组件。

```tsx
import React from 'react';
import { Form, Upload, UploadBtn, UploadImg } from 'parsec-admin';
import DocuApp from '../../AloneRouteDocuApp'

export default () => (
  <DocuApp>
    <Form
      items={[
        {
          name: 'file',
          required: true,
          label: '上传组件',
          render: <Upload><div style={{width: 100, height: 100, background: '#eee'}}>点击、拖拽上传</div></Upload>,
        },
        {
          name: 'btn',
          required: true,
          label: '上传按钮',
          render: <UploadBtn/>,
        },
        {
          name: 'img',
          required: true,
          label: '上传图片',
          render: <UploadImg/>,
        },
      ]}
    />
  </DocuApp>
);
```

## API

| 属性  | 说明                 | 默认值 |
| ----- | -------------------- | ------ |
| length  | 允许上传的文件数量。 | 1      |
| renderFileName | 渲染文件名。               | -      |
| maxSize | 上传文件最大大小。               | -      |
| onMaxError | 超出限制大小事件。               | -      |
| arrValue | 当length===1时生效，是否返回数组value。               | `true`      |
| onFileListChange | 当fileList改变时会执行。               | -      |
| fileValue | 返回file类型的值，不再通过上传方法进行上传。               | -      |
