# 数组单选器

根据 Options 方便地使用 Radio 组件。

```tsx
import React from 'react';
import { ArrRadio } from 'parsec-admin';
import { Space } from 'antd';

export default () => (
  <Space>
    <ArrRadio
      allowClear
      radios={[
        { children: '小明', value: 'XiaoMing' },
        { children: '小红', value: '<PERSON>H<PERSON>' },
      ]}
    />
  </Space>
);
```

## API

| 属性     | 说明                   | 默认值 |
| -------- | ---------------------- | ------ |
| options  | 渲染的选项。           | -      |
| allowClear | 是否允许清空。 | -      |

> 更多 API 请查看 [Radio](https://ant.design/components/radio-cn/#API)
