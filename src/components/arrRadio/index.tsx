import { Radio } from 'antd';
import { RadioGroupProps, RadioProps } from 'antd/lib/radio/interface';
import React from 'react';
import { useEffectState } from 'parsec-hooks';
import classnames from 'classnames';

export default ({
  radios,
  allowClear,
  ...props
}: RadioGroupProps &
  React.RefAttributes<HTMLDivElement> & {
    radios: (RadioProps & React.RefAttributes<HTMLElement>)[];
    /**
     * 是否允许清除
     */
    allowClear?: boolean;
  }) => {
  const [value, setValue] = useEffectState(props.value);
  return (
    <Radio.Group
      {...props}
      value={value}
      onChange={e => {
        props.onChange?.(e);
        setValue(e.target.value);
      }}
    >
      {radios.map(p => (
        <Radio
          {...p}
          key={p.key || p.value}
          className={classnames(p.className, `value-${p.value}`)}
          onClick={e => {
            p.onClick?.(e);
            if (allowClear) {
              props.onChange?.(undefined);
              setValue(undefined);
            }
          }}
        />
      ))}
    </Radio.Group>
  );
};
