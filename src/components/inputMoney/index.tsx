import React from 'react';
import { InputNumber } from 'antd';
import { InputNumberProps } from 'antd/lib/input-number';

const BigNumber = require('bignumber.js');
export interface UnitInputNumberProps extends InputNumberProps {
  unit?: React.ReactNode;
}

export default ({ onChange, value, unit, ...props }: UnitInputNumberProps) => (
  <span style={{ display: unit ? 'flex' : undefined, alignItems: 'center' }}>
    <InputNumber
      min={0}
      precision={2}
      type={'number'}
      onChange={v => {
        if (onChange && v !== undefined) {
          // 使用 bignumber 进行精确计算
          const amount = new BigNumber(v).multipliedBy(100);
          onChange(amount.toNumber());
        }
      }}
      value={
        value === undefined
          ? value
          : new BigNumber(value).dividedBy(100).toNumber()
      }
      {...props}
    />
    {unit && <span style={{ marginLeft: 10 }}>{unit}</span>}
  </span>
);
