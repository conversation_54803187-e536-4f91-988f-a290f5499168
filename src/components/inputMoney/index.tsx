import React from 'react';
import { InputNumber } from 'antd';
import { InputNumberProps } from 'antd/lib/input-number';

export interface UnitInputNumberProps extends InputNumberProps {
  unit?: React.ReactNode;
}

export default ({ onChange, value, unit, ...props }: UnitInputNumberProps) => (
  <span style={{ display: unit ? 'flex' : undefined, alignItems: 'center' }}>
    <InputNumber
      min={0}
      precision={2}
      type={'number'}
      onChange={(v) => {
        if (onChange && v !== undefined) {
          onChange(+v * 100);
        }
      }}
      value={value === undefined ? value : +value / 100}
      {...props}
    />
    {unit && <span style={{ marginLeft: 10 }}>{unit}</span>}
  </span>
);
