# 金额输入

用于输入以分为单位的输入框，以分为单位传入，会以元显示，提交则又会转换为以分单位。

```tsx
import React from 'react';
import { InputMoney } from 'parsec-admin';
import { useForceUpdate, useInit } from 'parsec-hooks';
import { Form } from 'antd';

const FormItem = Form.Item;

export default () => {
  const { forceUpdate } = useForceUpdate();
  // 第一次进来更新一下
  useInit(forceUpdate);
  const [form] = Form.useForm();
  return (
    <Form
      form={form}
      // 每次输入强制更新，让下面的 moneyValue 能同步更新
      onValuesChange={forceUpdate}
      initialValues={{ money: 1000 }}
    >
      <FormItem label={'金额'} name={'money'}>
        <InputMoney unit={'元'} />
      </FormItem>
      <br />
      moneyValue: {form.getFieldValue('money')}
    </Form>
  );
};
```

## API

| 属性 | 说明                   | 默认值 |
| ---- | ---------------------- | ------ |
| unit | input 后面显示的单位。 | -      |

> 更多使用方法查看 [InputNumber](https://ant.design/components/input-number-cn/#API) 。
