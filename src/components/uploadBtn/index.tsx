import React from 'react';
import { Button } from 'antd';
import { ButtonProps } from 'antd/lib/button';
import MyUpload, { MyUploadProps } from '../upload';
import { UploadOutlined } from '@ant-design/icons';

interface UploadBtnProps extends MyUploadProps {
  text?: React.ReactNode;
  buttonProps?: ButtonProps;
}

export default ({
  length = 1,
  accept = 'image/*',
  text = '浏览',
  showUploadList,
  value,
  buttonProps,
  ...props
}: UploadBtnProps) => (
  <MyUpload
    accept={accept}
    length={length}
    value={value}
    showUploadList={showUploadList}
    {...props}
  >
    <Button
      type={'primary'}
      icon={<UploadOutlined />}
      disabled={value && value.length >= length}
      {...buttonProps}
    >
      {text}
    </Button>
  </MyUpload>
);
