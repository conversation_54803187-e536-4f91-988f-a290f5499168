import React, { useMemo } from 'react';
import { Select } from 'antd';
import { SelectProps, SelectValue } from 'antd/lib/select';
import { OptionProps } from 'rc-select/lib/Option';
import classNames from 'classnames';
import styled from 'styled-components';

const { Option } = Select;

export interface ArrSelectProps
  extends Omit<SelectProps<SelectValue>, 'options'> {
  options:
    | OptionProps[]
    | { [value: string]: any; [value: number]: any }
    | (string | number)[];
  renderOptionProps?: (option: OptionProps) => OptionProps;
  arrValue?: boolean;
  /**
   * 使用字符串类型的值
   */
  stringValue?: boolean;
}

export const transferOptions = (
  options: ArrSelectProps['options'],
  stringValue = false,
) =>
  (options instanceof Array
    ? typeof options[0] === 'object'
      ? options
      : (options as []).map(item => ({ children: item, value: item }))
    : Object.keys(options).map(key => ({
        children: options[key],
        value: +key + 1 && key !== '' && !stringValue ? +key : key,
      }))) as OptionProps[];

/**
 * 传数组或者对象就会返回Select
 */
export default ({
  renderOptionProps = option => option,
  options,
  arrValue,
  value,
  onChange,
  mode,
  stringValue,
  ...props
}: ArrSelectProps) => {
  const isMultiple = mode === 'multiple';
  value = useMemo<any>(
    () => (value ? (arrValue && !isMultiple ? value[0] : value) : value),
    [arrValue, value, isMultiple],
  );
  options = transferOptions(options, stringValue);
  return (
    <MySelect
      placeholder={'请选择'}
      allowClear
      mode={mode}
      value={value}
      onChange={(v, o: any) =>
        onChange && onChange((arrValue && !isMultiple ? [v] : v) as any, o)
      }
      showSearch
      filterOption={(input, option) =>
        ('' + option?.children + option?.value)
          .toLowerCase?.()
          .indexOf(input.toLowerCase()) >= 0
      }
      dropdownMatchSelectWidth={false}
      // filterSort={(optionA, optionB) =>
      //   (optionA.children as any)
      //     .toLowerCase?.()
      //     .localeCompare((optionB.children as any).toLowerCase())
      // }
      {...props}
    >
      {options.map((props, index) => (
        <Option
          key={props.key || index}
          {...props}
          children={props.children || props.label}
          className={classNames(
            props.className,
            `option-${props.children || props.label}`,
          )}
          {...renderOptionProps(props)}
          value={
            props.value !== undefined && stringValue
              ? props.value + ''
              : props.value
          }
        />
      ))}
    </MySelect>
  );
};

const MySelect = styled(Select)`
  && {
    display: block;
  }
`;
