# 数组选择器

根据 Options 方便地使用 Select 组件。

```tsx
import React from 'react';
import { ArrSelect } from 'parsec-admin';
import { Space } from 'antd';

export default () => (
  <Space>
    <ArrSelect options={[1, 2, 3]} />
    <ArrSelect options={{ 1: '已启用', 2: '未启用' }} />
    <ArrSelect
      options={[
        { children: '小明', value: 'XiaoMing' },
        { children: '小红', value: '<PERSON>Hong' },
      ]}
    />
  </Space>
);
```

## API

| 属性     | 说明                   | 默认值 |
| -------- | ---------------------- | ------ |
| options  | 渲染的选项。           | -      |
| arrValue | 是否返回数组形式的值。 | -      |

> 更多 API 请查看 [Select](https://ant.design/components/select-cn/#API)
