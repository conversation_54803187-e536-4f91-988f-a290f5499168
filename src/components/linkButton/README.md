# 链接按钮

跟 a 标签一样样式的按钮。

```tsx
import React from 'react';
import { LinkButton } from 'parsec-admin';
import { message } from 'antd';

export default () => (
  <LinkButton onClick={() => message.success('我是按钮')}>我是按钮</LinkButton>
);
```

## 同时使用多个

通常跟 ActionsWrap 配合在 Table 里使用。

```tsx
import React from 'react';
import { LinkButton, ActionsWrap } from 'parsec-admin';

export default () => (
  <ActionsWrap>
    <LinkButton>编辑</LinkButton>
    <LinkButton>详情</LinkButton>
    <LinkButton>删除</LinkButton>
  </ActionsWrap>
);
```
