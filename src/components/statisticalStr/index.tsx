import React from 'react';
import { Tooltip } from 'antd';

export default ({ value, unit }: { value: number; unit?: React.ReactNode }) => {
  let showValue: number | string = value < 0 ? value * -1 : value;
  let showText = '';
  if (showValue >= 1000000) {
    showValue /= 1000000;
    showText = '百万';
  } else if (showValue >= 10000) {
    showValue /= 10000;
    showText = '万';
  }
  showValue = showValue.toString().split('.')[0];
  return (
    <Tooltip
      title={
        <>
          {value}
          {unit}
        </>
      }
    >
      <span>
        {value < 0 ? '-' : ''}
        {showValue}
        {showText}
        {unit}
      </span>
    </Tooltip>
  );
};
