# 统计字符串

显示统计值的大概数值。

```tsx
import React from 'react';
import { StatisticalStr } from 'parsec-admin';
import { Space } from 'antd';

export default () => (
  <Space>
    <StatisticalStr value={1000} />
    <StatisticalStr value={12342} unit={'人'} />
    <StatisticalStr value={534432} unit={'元'} />
    <StatisticalStr value={6545324} unit={'字节'} />
  </Space>
);
```

## API

| 属性  | 说明                 | 默认值 |
| ----- | -------------------- | ------ |
| unit  | 数值后面显示的单位。 | -      |
| value | 数值。               | -      |
