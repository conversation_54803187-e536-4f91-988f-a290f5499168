# 选择表单项

同时保存选择组件的 value 和 label 字段。

```tsx
import React from 'react';
import { SelectFormItem, ArrSelect } from 'parsec-admin';
import { Form, Button } from 'antd';

const options = [{value: 1, label: '男'}, {value: 2, label: '女'}]

export default () => {
  const [form] = Form.useForm()
  return <Form form={form} onFinish={values => alert(JSON.stringify(values))}>
    <SelectFormItem label={'选择'} name={'value'} labelName={'label'} options={options}>
      <ArrSelect options={options}/>
    </SelectFormItem>
    <Button onClick={() => form.submit()}>提交</Button>
  </Form>
};
```

## API

| 属性        | 说明       | 默认值 |
|-----------|----------| ------ |
| labelName | label的字段名。 | -      |
| options   | 选项。      | -      |
