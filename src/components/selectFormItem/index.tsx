import React from 'react';
import { Form, Input } from 'antd';
import { FormItemProps } from 'antd/lib/form/FormItem';
import { ArrSelectProps, transferOptions } from '../arrSelect';

export default ({
  labelName,
  children,
  trigger = 'onChange',
  options = [],
  ...props
}: FormItemProps<any> & {
  labelName?: string;
  options?: ArrSelectProps['options'];
}) => {
  return (
    <>
      <Form.Item noStyle shouldUpdate>
        {form => (
          <Form.Item {...props} trigger={trigger}>
            {React.isValidElement(children)
              ? React.cloneElement(children, {
                  ...children.props,
                  [trigger]: (...arg) => {
                    const find = transferOptions(options).find(
                      ({ value }) => value == arg[0],
                    );
                    form.setFieldsValue({
                      [labelName]: find?.children || find?.label,
                    });
                    children.props[trigger]?.(...arg);
                  },
                })
              : children}
          </Form.Item>
        )}
      </Form.Item>
      {labelName && (
        <Form.Item name={labelName} style={{ display: 'none' }}>
          <Input />
        </Form.Item>
      )}
    </>
  );
};
