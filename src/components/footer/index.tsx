import React from 'react';
import styled from 'styled-components';
import { Layout } from 'antd';
import AppStore from '../../stores/AppStore';

export default ({
  className,
  copyright = AppStore.useContainer().copyright,
}: {
  className?: string;
  copyright?: React.ReactNode;
}) => {
  return copyright !== false ? (
    <MyFooter className={className}>{copyright}</MyFooter>
  ) : (
    <MyFooter className={className} />
  );
};

const MyFooter = styled(Layout.Footer)`
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  display: flex;
  align-items: flex-end;
  flex: 1;
  line-height: 1;
  justify-content: center;
  padding: 0 0 50px;
  .anticon {
    margin: 0 4px;
  }
`;
