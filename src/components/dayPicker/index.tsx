import React from 'react';
import { DatePicker } from 'antd';
import { DatePickerProps } from 'antd/lib/date-picker';
import moment from 'moment';

export default ({
  onChange,
  value,
  stringValue,
  valueFormat,
  picker,
  ...props
}: DatePickerProps & {
  /**
   * 字符串的值
   */
  stringValue?: boolean;
  /**
   * 值的格式
   */
  valueFormat?: string;
}) => (
  <DatePicker
    allowClear
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    value={value ? moment(value) : value}
    {...props}
    picker={picker}
    onChange={(date: any, dateString) => {
      if (!date) {
        return onChange?.(undefined, undefined);
      }
      if (date && !(props as any).showTime && picker !== 'time') {
        date = moment(date.format('YYYY-MM-DD 00:00:00'));
      }
      if (onChange) {
        onChange(
          valueFormat
            ? moment(date).format(valueFormat)
            : stringValue
            ? moment(date).toISOString()
            : date,
          dateString,
        );
      }
    }}
  />
);
