# 日期选择器

选择的日期会格式化到选择日期的 0 时 0 分 0 秒，常用在搜索项。

```tsx
import React, { useState } from 'react';
import { DayPicker, DateShow } from 'parsec-admin';
import { Moment } from 'moment';

export default () => {
  const [value, setValue] = useState<Moment>();
  return (
    <>
      <DayPicker onChange={setValue} />
      <br />
      <br />
      selected: <DateShow>{value}</DateShow>
    </>
  );
};
```

传入值之前可以不转为 `moment` 对象。

```tsx
import React from 'react';
import { DayPicker } from 'parsec-admin';

export default () => {
  return <DayPicker value={'2020-5-20'} />;
};
```

## API

| 属性        | 说明               | 默认值 |
| ----------- | ------------------ | ------ |
| stringValue | 将值转成ISO字符串。 | -      |
| valueFormat | 将值转为什么格式。 | -      |

> 更多参考 [DatePicker](https://ant.design/components/date-picker-cn/#API) 。
