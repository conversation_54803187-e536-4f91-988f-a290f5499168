import React, { useCallback, useEffect } from 'react';
import { DatePicker } from 'antd';
import moment from 'moment';
import { RangePickerProps as BaseRangePickerProps } from 'antd/lib/date-picker/generatePicker';
import { useRef } from 'react';

const { RangePicker } = DatePicker;

export default ({
  onChange,
  value,
  valueFormat,
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  showTime,
  stringValue,
  picker,
  ...props
}: BaseRangePickerProps<moment.Moment> & {
  /**
   * 格式Value
   */
  valueFormat?: string;
  /**
   * 字符串的值
   */
  stringValue?: boolean;
}) => {
  const preValue = useRef(`${JSON.stringify(value)}`);
  const handleChange = useCallback(
    (dates: any, dateStrings) => {
      preValue.current = `${JSON.stringify(dates)}`;
      dates = dates ? [...dates] : undefined;
      if (dates) {
        if (!showTime && picker !== 'time') {
          if (dates[0]) {
            dates[0] = moment(moment(dates[0]).format('YYYY-MM-DD 00:00:00'));
          }
          if (dates[1]) {
            dates[1] = moment(moment(dates[1]).format('YYYY-MM-DD 23:59:59'));
          }
        }
        if (valueFormat || stringValue) {
          dates.forEach((date: moment.Moment, index: number) => {
            dates[index] = valueFormat
              ? moment(date).format(valueFormat)
              : moment(date).toISOString();
          });
        }
      }
      if (onChange) {
        onChange(dates, dateStrings);
      }
    },
    [onChange, picker, showTime, stringValue, valueFormat],
  );
  useEffect(() => {
    const newValue = `${JSON.stringify(value)}`;
    if (newValue !== preValue.current) {
      handleChange(value, [value?.[0] + '', value?.[1] + '']);
    }
  }, [handleChange, value]);
  return (
    <RangePicker
      allowClear
      showTime={showTime}
      picker={picker}
      value={
        value && value instanceof Array
          ? value.map(v => (v ? moment(v) : undefined))
          : value
      }
      {...(props as any)}
      onChange={handleChange}
    />
  );
};
