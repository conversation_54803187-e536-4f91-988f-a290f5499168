# 日期范围选择器

选择的日期范围会格式化到开始日期的 0 时 0 分 0 秒和结束日期的 23 时 59 分 59 秒，常用在搜索项。

```tsx
import React, { useState } from 'react';
import { DayRangePicker } from 'parsec-admin';
import { Moment } from 'moment';

export default () => {
  const [value = [], setValue] = useState<Moment>();
  return (
    <>
      <DayRangePicker valueFormat={'YYYY-MM-DD HH:mm:ss'} onChange={setValue} />
      <br />
      <br />
      selected: {value[0]} - {value[1]}
    </>
  );
};
```

传入值之前可以不转为 `moment` 对象。

```tsx
import React from 'react';
import { DayRangePicker } from 'parsec-admin';

export default () => {
  return <DayRangePicker value={['2020-5-20', '2020-5-21']} />;
};
```

## API

| 属性        | 说明               | 默认值 |
| ----------- | ------------------ | ------ |
| valueFormat | 将值转为什么格式。 | -      |
| stringValue | 将值转成ISO字符串。 | -      |

> 更多用法参考 [DatePicker](https://ant.design/components/date-picker-cn/#DatePicker) 。
