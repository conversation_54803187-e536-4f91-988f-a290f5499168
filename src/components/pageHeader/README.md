# 页头

默认不需要设置 `breadcrumb` 和 `title` 也会根据 App routes 配置自动显示。

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { PageHeader } from 'parsec-admin';
import { Button, Descriptions } from 'antd';

export default () => (
  <PageHeader
    ghost={false}
    onBack={() => window.history.back()}
    title="Title"
    subTitle="This is a subtitle"
    extra={[
      <Button key="3">Operation</Button>,
      <Button key="2">Operation</Button>,
      <Button key="1" type="primary">
        Primary
      </Button>,
    ]}
  >
    <Descriptions size="small" column={3}>
      <Descriptions.Item label="Created">Lili Qu</Descriptions.Item>
      <Descriptions.Item label="Association">
        <a>421421</a>
      </Descriptions.Item>
      <Descriptions.Item label="Creation Time">2017-01-10</Descriptions.Item>
      <Descriptions.Item label="Effective Time">2017-10-10</Descriptions.Item>
      <Descriptions.Item label="Remarks">
        Gonghu Road, Xihu District, Hangzhou, Zhejiang, China
      </Descriptions.Item>
    </Descriptions>
  </PageHeader>
);
```

## 粘性定位

设置 `sticky` 后，可实现粘性定位。

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { PageHeader, BlankSection } from 'parsec-admin';
import { Button, Descriptions, Typography, Divider } from 'antd';
import styled from 'styled-components';

const { Title, Paragraph, Text } = Typography;

const Page = styled.div`
  height: 500px;
  overflow: auto;
`;

export default () => (
  <Page>
    <PageHeader
      sticky
      ghost={false}
      onBack={() => window.history.back()}
      title="Title"
      subTitle="This is a subtitle"
      extra={[
        <Button key="3">Operation</Button>,
        <Button key="2">Operation</Button>,
        <Button key="1" type="primary">
          Primary
        </Button>,
      ]}
    >
      <Descriptions size="small" column={3}>
        <Descriptions.Item label="Created">Lili Qu</Descriptions.Item>
        <Descriptions.Item label="Association">
          <a>421421</a>
        </Descriptions.Item>
        <Descriptions.Item label="Creation Time">2017-01-10</Descriptions.Item>
        <Descriptions.Item label="Effective Time">2017-10-10</Descriptions.Item>
        <Descriptions.Item label="Remarks">
          Gonghu Road, Xihu District, Hangzhou, Zhejiang, China
        </Descriptions.Item>
      </Descriptions>
    </PageHeader>
    <BlankSection>
      <Typography>
        <Title>Introduction</Title>
        <Paragraph>
          In the process of internal desktop applications development, many
          different design specs and implementations would be involved, which
          might cause designers and developers difficulties and duplication and
          reduce the efficiency of development.
        </Paragraph>
        <Paragraph>
          After massive project practice and summaries, Ant Design, a design
          language for background applications, is refined by Ant UED Team,
          which aims to
          <Text strong>
            uniform the user interface specs for internal background projects,
            lower the unnecessary cost of design differences and implementation
            and liberate the resources of design and front-end development
          </Text>.
        </Paragraph>
        <Title level={2}>Guidelines and Resources</Title>
        <Paragraph>
          We supply a series of design principles, practical patterns and high
          quality design resources (<Text code>Sketch</Text> and{' '}
          <Text code>Axure</Text>), to help people create their product
          prototypes beautifully and efficiently.
        </Paragraph>

        <Paragraph>
          <ul>
            <li>
              <a>Principles</a>
            </li>
            <li>
              <a>Patterns</a>
            </li>
            <li>
              <a>Resource Download</a>
            </li>
          </ul>
        </Paragraph>

        <Divider />

        <Title>介绍</Title>
        <Paragraph>
          蚂蚁的企业级产品是一个庞大且复杂的体系。这类产品不仅量级巨大且功能复杂，而且变动和并发频繁，常常需要设计与开发能够快速的做出响应。同时这类产品中有存在很多类似的页面以及组件，可以通过抽象得到一些稳定且高复用性的内容。
        </Paragraph>
        <Paragraph>
          随着商业化的趋势，越来越多的企业级产品对更好的用户体验有了进一步的要求。带着这样的一个终极目标，我们（蚂蚁金服体验技术部）经过大量的项目实践和总结，逐步打磨出一个服务于企业级产品的设计体系
          Ant Design。基于<Text mark>『确定』和『自然』</Text>
          的设计价值观，通过模块化的解决方案，降低冗余的生产成本，让设计者专注于
          <Text strong>更好的用户体验</Text>。
        </Paragraph>
        <Title level={2}>设计资源</Title>
        <Paragraph>
          我们提供完善的设计原则、最佳实践和设计资源文件（
          <Text code>Sketch</Text> 和<Text code>Axure</Text>
          ），来帮助业务快速设计出高质量的产品原型。
        </Paragraph>

        <Paragraph>
          <ul>
            <li>
              <a>设计原则</a>
            </li>
            <li>
              <a>设计模式</a>
            </li>
            <li>
              <a>设计资源</a>
            </li>
          </ul>
        </Paragraph>
      </Typography>
    </BlankSection>
  </Page>
);
```

## API

| 属性   | 说明         | 默认值  |
| ------ | ------------ | ------- |
| sticky | 是否粘性定位 | `false` |

> 更多使用方法查看 [PageHeader](https://ant.design/components/page-header-cn/#API) 。
