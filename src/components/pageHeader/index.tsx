import React, { useEffect, useMemo, useRef, useState } from 'react';
import { RouteConfig } from '../../types';
import { PageHeader } from 'antd';
import { PageHeaderProps } from 'antd/lib/page-header';
import { BreadcrumbProps } from 'antd/lib/breadcrumb';
import stickyHooks from '../../utils/isStickyHooks';
import AppStore from '../../stores/AppStore';
import { appData } from '../../app';
import styled from 'styled-components';
import useGetBreadcrumbs from './getBreadcrumbsHooks';

export type MyPageHeaderProps = Omit<PageHeaderProps, 'title'> & {
  children?: React.ReactNode;
  title?: React.ReactNode;
} & (PageHeaderProps | Record<string, unknown>) & {
    sticky?: true;
    breadcrumb?: BreadcrumbProps | false;
  };

export default (props: MyPageHeaderProps) => {
  const { sticky } = props;
  const [homePath, setHomePath] = useState('');
  const { routes = useMemo(() => [], []) } = AppStore.useContainer();
  useEffect(() => {
    const { permissions } = appData.storeValue;
    let fined = false;
    const getHomePath = (routes: RouteConfig[]) =>
      routes.forEach(config => {
        const { inMenu = true, path, children } = config;
        if (fined) return;
        if (
          inMenu &&
          path &&
          permissions({ permissionsId: config.routeId, ...config })
        ) {
          fined = true;
          setHomePath(path + '');
        } else if (children) {
          getHomePath(children);
        }
      });
    getHomePath(routes);
  }, [homePath, routes]);

  const headerRef = useRef<HTMLDivElement>(null);
  const isSticky = stickyHooks(headerRef);

  const { breadcrumbs, itemRender } = useGetBreadcrumbs(homePath, routes);

  return (
    <div
      ref={headerRef}
      style={
        sticky
          ? {
              position: 'sticky',
              top: 0,
              zIndex: 3,
              boxShadow: isSticky ? '0 1px 4px rgba(0,21,41,0.08)' : undefined,
            }
          : undefined
      }
    >
      <MyPageHeader
        title={breadcrumbs[breadcrumbs.length - 1]?.breadcrumbName}
        {...props}
        key={+new Date()}
        breadcrumb={
          props.breadcrumb === false
            ? undefined
            : props.breadcrumb || {
                routes: breadcrumbs,
                itemRender,
              }
        }
      />
    </div>
  );
};

const MyPageHeader = styled(PageHeader)`
  &&& {
    background-color: var(--component-background);
  }
`;

export { useGetBreadcrumbs };
