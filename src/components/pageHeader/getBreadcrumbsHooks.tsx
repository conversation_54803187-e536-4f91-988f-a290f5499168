import { Link, matchPath, useLocation } from 'react-router-dom';
import React, { useEffect, useMemo, useState } from 'react';
import { RouteConfig } from '../../types';
import { Route } from 'antd/lib/breadcrumb/Breadcrumb';

export default (homePath: string, routes: RouteConfig[]) => {
  const [breadcrumbRoutes, setBreadcrumbRoutes] = useState<
    { path: string; breadcrumbName: string }[]
  >([]);
  const { pathname } = useLocation();
  const [currentRouteConfig, setCurrentRouteConfig] = useState<RouteConfig>();
  useEffect(() => {
    if (!homePath) return;
    let fined = false;
    const newBreadcrumbRoutes: {
      path: string;
      breadcrumbName: string;
    }[] = [];
    const findFn = (routes: RouteConfig[]) => {
      routes.forEach(props => {
        if (fined) return;
        const children = props.children;
        const path = `${
          children && !props.component ? children[0].path : props.path
        }`;
        if (children) {
          findFn(children);
        }
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        fined = !!matchPath(pathname, { exact: !children, ...props, path });
        if (fined) {
          setCurrentRouteConfig(props);
          newBreadcrumbRoutes.push({
            path: `${path}|${Math.random()}`,
            breadcrumbName: props.name,
          });
        }
      });
    };
    findFn(routes);
    setBreadcrumbRoutes(newBreadcrumbRoutes.reverse());
  }, [homePath, pathname, routes]);
  const homeBreadcrumb = useMemo(
    () =>
      breadcrumbRoutes.find(({ breadcrumbName }) => breadcrumbName === '首页'),
    [breadcrumbRoutes],
  );
  const breadcrumbs = useMemo<{ breadcrumbName: string; path: string }[]>(
    () => [
      ...(!homeBreadcrumb
        ? [{ path: homeBreadcrumb?.path || '/', breadcrumbName: '首页' }]
        : []),
      ...breadcrumbRoutes,
    ],
    [breadcrumbRoutes, homeBreadcrumb],
  );
  return {
    breadcrumbs,
    currentRouteConfig,
    itemRender: (route: Route) => {
      const paramsPath = pathname.match(
        route.path.split('|')[0].replace(/\/:\w+/g, '/\\d+'),
      );
      const to = /\/:\w+/g.test(route.path)
        ? paramsPath
          ? paramsPath[0]
          : undefined
        : route.path;
      return to ? (
        <Link to={to}>{route.breadcrumbName}</Link>
      ) : (
        route.breadcrumbName
      );
    },
  };
};
