import { Checkbox, Divider, Dropdown, <PERSON>u, Popover, Row, Tooltip } from 'antd';
import LinkButton from '../../components/linkButton';
import React, { useCallback, useMemo } from 'react';
import { SizeType } from 'antd/lib/config-provider/SizeContext';
import RoutesTreeStore from '../../stores/RoutesTreeStore';
import styled from 'styled-components';
import {
  SettingOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  ColumnHeightOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons';
import { TableListColumnProps } from './Types';
import { getPrefixCls } from '../../_utils';

interface Props {
  toggleFull: () => void;
  selectedIds: string[];
  columnsDataIndex: any[];
  fullscreenRef: React.MutableRefObject<HTMLDivElement> | undefined;
  tableTitle: React.ReactNode;
  showTool: boolean;
  action?: React.ReactNode;
  setTableSize: (size: SizeType) => void;
  setSelectedIds: (ids: string[]) => void;
  isFullscreen: boolean;
  tableSize: SizeType;
  extra?: React.ReactNode;
  selectItem: (id: any) => void;
  basicTableColumns: TableListColumnProps<any>[];
}

export default function({
  toggleFull,
  setSelectedIds,
  setTableSize,
  action,
  showTool,
  tableTitle,
  selectedIds,
  columnsDataIndex,
  fullscreenRef,
  tableSize,
  isFullscreen,
  selectItem,
  extra,
  basicTableColumns,
}: Props) {
  const { reloadList } = RoutesTreeStore.useContainer();
  const tableSizes = useMemo<{ text: string; size: SizeType }[]>(
    () => [
      {
        size: undefined,
        text: '默认',
      },
      {
        size: 'middle',
        text: '中等',
      },
      {
        size: 'small',
        text: '紧凑',
      },
    ],
    [],
  );
  const columnSettingChecked = useMemo(() => !!selectedIds.length, [
    selectedIds.length,
  ]);
  const columnSettingIndeterminate = useMemo(
    () =>
      selectedIds.length !== columnsDataIndex.length && !!selectedIds.length,
    [columnsDataIndex.length, selectedIds.length],
  );
  const getPopupContainer = useCallback(
    () => fullscreenRef?.current || document.body,
    [fullscreenRef],
  );
  return (
    <TableListCardHeader className={'tableList-header'}>
      <ListActionsWrap>
        <TableTitle className={'tableList-header-title'}>
          {tableTitle}
        </TableTitle>
        <div
          className={'tableList-header-action'}
          style={{
            display: 'flex',
            marginBottom: 16,
            alignItems: 'center',
          }}
        >
          {action && (
            <>
              <ListActions>{action}</ListActions>
              {showTool && action && (
                <Divider style={{ marginLeft: 16 }} type={'vertical'} />
              )}
            </>
          )}
          {showTool && (
            <ActionIcons>
              <Tooltip title={'密度'} getPopupContainer={getPopupContainer}>
                <Dropdown
                  getPopupContainer={getPopupContainer}
                  trigger={['click']}
                  overlay={
                    <Menu style={{ width: 80 }} selectedKeys={[tableSize + '']}>
                      {tableSizes.map(({ size, text }) => (
                        <Menu.Item
                          onClick={() => setTableSize(size)}
                          key={size + ''}
                        >
                          {text}
                        </Menu.Item>
                      ))}
                    </Menu>
                  }
                >
                  <ColumnHeightOutlined />
                </Dropdown>
              </Tooltip>
              <Tooltip
                title={isFullscreen ? '退出全屏' : '全屏'}
                getPopupContainer={getPopupContainer}
              >
                {isFullscreen ? (
                  <FullscreenExitOutlined onClick={() => toggleFull()} />
                ) : (
                  <FullscreenOutlined onClick={() => toggleFull()} />
                )}
              </Tooltip>
              <Tooltip title={'刷新'} getPopupContainer={getPopupContainer}>
                <ReloadOutlined onClick={() => reloadList()} />
              </Tooltip>
              <Tooltip title={'列设置'} getPopupContainer={getPopupContainer}>
                <Popover
                  getPopupContainer={getPopupContainer}
                  placement="bottomRight"
                  trigger="click"
                  arrowPointAtCenter
                  content={
                    <ColumnSettingContent>
                      {basicTableColumns
                        .filter(({ dataIndex }) => dataIndex)
                        .map(({ dataIndex, title }) => (
                          <Checkbox
                            checked={selectedIds.includes(dataIndex as string)}
                            onClick={() => selectItem(dataIndex)}
                            key={dataIndex + ''}
                          >
                            {/*eslint-disable-next-line @typescript-eslint/ban-ts-comment*/}
                            {/*@ts-ignore*/}
                            {title}
                          </Checkbox>
                        ))}
                    </ColumnSettingContent>
                  }
                  title={
                    <div style={{ padding: '5px 0' }}>
                      <Checkbox
                        onClick={() =>
                          columnSettingIndeterminate
                            ? setSelectedIds(columnsDataIndex)
                            : columnSettingChecked
                            ? setSelectedIds([])
                            : setSelectedIds(columnsDataIndex)
                        }
                        checked={columnSettingChecked}
                        indeterminate={columnSettingIndeterminate}
                      >
                        列展示
                      </Checkbox>
                      <LinkButton
                        style={{ float: 'right' }}
                        onClick={() => setSelectedIds(columnsDataIndex)}
                      >
                        重置
                      </LinkButton>
                    </div>
                  }
                >
                  <SettingOutlined />
                </Popover>
              </Tooltip>
            </ActionIcons>
          )}
        </div>
      </ListActionsWrap>
      {extra && <div style={{ marginBottom: 16 }}>{extra}</div>}
    </TableListCardHeader>
  );
}

const ColumnSettingContent = styled.div`
  min-width: 168px;
  .${getPrefixCls}-checkbox-wrapper {
    display: block;
    margin: 0 0 8px !important;
    display: flex;
    &:last-child {
      margin-bottom: 0 !important;
    }
  }
`;

const TableTitle = styled.h3`
  font-size: 16px;
  line-height: 24px;
  opacity: 0.85;
  font-weight: 400;
  flex: 1;
  white-space: nowrap;
  margin-bottom: 16px;
`;

const TableListCardHeader = styled.header`
  padding: 16px 24px 0;
`;

const ActionIcons = styled.div`
  font-size: 16px;
  display: flex;
  align-items: center;
  .anticon {
    margin-left: 16px;
    cursor: pointer;
    &:first-child {
      margin-left: 0;
    }
  }
`;

const ListActions = styled.div`
  > *:not(:last-child) {
    margin-right: 8px;
  }
`;

const ListActionsWrap = styled(Row)`
  align-items: center;
`;
