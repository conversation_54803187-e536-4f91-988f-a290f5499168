import { Col, Input, Form } from 'antd';
import React from 'react';
import styled from 'styled-components';
import { FormItemProps } from 'antd/lib/form';
import { ColProps } from 'antd/lib/col';
import { getPrefixCls } from '../../_utils';
import classnames from 'classnames';

interface Props<D> {
  search?: boolean | React.ReactElement;
  dataIndex?: keyof D | (keyof D)[];
  title: React.ReactNode;
  colProps?: ColProps;
  formItemProps?: FormItemProps;
  className?: string;
  show: boolean;
}

export default function<D>({
  dataIndex,
  title,
  search,
  colProps,
  formItemProps,
  className,
  show,
}: Props<D>) {
  return search ? (
    <SearchItemCol show={show ? 1 : 0} {...colProps} className={className}>
      <FormItem
        name={
          typeof dataIndex === 'string'
            ? dataIndex
            : dataIndex
            ? dataIndex[0]
            : dataIndex
        }
        label={title}
        {...formItemProps}
        className={classnames(`form-item-${title}`, formItemProps?.className)}
      >
        {search === true ? <Input placeholder={`请输入${title}`} /> : search}
      </FormItem>
    </SearchItemCol>
  ) : null;
}

const FormItem = styled(Form.Item)<FormItemProps>`
  &&& {
    display: flex;
    margin-right: 0;
    margin-bottom: 24px;
    .${getPrefixCls}-picker {
      width: 100%;
    }
    @media (max-width: 575px) {
      display: inline;
      margin-bottom: 0;
      .${getPrefixCls}-form-item-control {
        display: block;
      }
      .${getPrefixCls}-form-item-label {
        text-align: left !important;
      }
    }
    .${getPrefixCls}-form-item-label, .${getPrefixCls}-form-item-control {
      line-height: 32px;
    }
    .${getPrefixCls}-form-item-label {
      min-width: 80px;
      text-align: right;
      padding-right: 8px;
    }
    .${getPrefixCls}-form-item-row {
      flex: 1;
    }
    .${getPrefixCls}-form-item-control-wrapper {
      flex: 1;
    }
    .${getPrefixCls}-form-item-control {
      flex: 1;
      width: 0;
    }
  }
`;

export const SearchItemCol = styled((props: ColProps & { show?: 1 | 0 }) => (
  <Col
    xs={{ span: 24 }}
    sm={{ span: 12 }}
    md={{ span: 12 }}
    lg={{ span: 8 }}
    xxl={{ span: 6 }}
    {...props}
  />
))`
  && {
    padding: 4px 8px;
    display: ${({ show = 1 }) => (show ? 'block' : 'none')};
    @media (max-width: 1600px) {
      &.SearchItem2 {
        display: none;
      }
    }
    @media (max-width: 990px) {
      &.SearchItem1 {
        display: none;
      }
    }
  }
`;
