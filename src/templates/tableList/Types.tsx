import React from 'react';
import { ColProps } from 'antd/lib/col';
import { FormItemProps } from 'antd/lib/form';
import { BaseTableProps } from '../../components/baseTable';
import { MyPageHeaderProps } from '../../components/pageHeader';
import { FormInstance } from 'antd/lib/form/hooks/useForm';
import { FormProps } from 'antd/lib/form';
import { ExportExcelButtonProps } from './ExportExcelButton';

export type TableListColumnProps<D> = Omit<
  BaseTableProps<D, any>['columns'][number],
  'render' | 'dataIndex' | 'fixed'
> & {
  fixed?: boolean | string; // left right
  search?: boolean | React.ReactElement;
  colProps?: ColProps;
  dataIndex?: string | string[];
  formItemProps?: FormItemProps;
  searchIndex?: string | string[];
  /**
   * 搜索项排序
   */
  searchSort?: number;
  /**
   * 为false则不会出现来表格里
   */
  render?: ((text: any, record: D, index: number) => React.ReactNode) | false;
  /**
   * excel渲染
   * 为false则不会出现来表格里
   */
  excelRender?:
    | ((text: any, record: D, index: number) => string | number)
    | false;
  /**
   * excel每列宽度
   * 屏幕宽度为100 20即为 1/5屏幕大小
   * https://www.npmjs.com/package/js-export-excel#sheet-option
   */
  excelColumnWidth?: number;
};

export interface TableListProps<D, P>
  extends Omit<BaseTableProps<D, P>, 'columns'> {
  columns?: (
    | TableListColumnProps<D>
    | ((searchValues: any) => TableListColumnProps<D>)
  )[];
  pageHeaderProps?: MyPageHeaderProps | false;
  form?: FormInstance;
  action?: React.ReactNode;
  extra?: React.ReactNode;
  /**
   * 默认：查询表格
   */
  tableTitle?: React.ReactNode;
  /**
   * 默认true
   */
  showTool?: boolean;
  /**
   * Tool里显示的ColumnsDataIndex
   */
  showColumnsDataIndex?: string[];
  /**
   * Tool里显示的ColumnsDataIndex发生改变
   */
  onShowColumnsDataIndexChange?: (dataIndexArr: string[]) => void;
  searchFormProps?: FormProps;
  /**
   * 简洁模式，只展示搜索项和table
   */
  succinct?: boolean;
  /**
   * 显示导出按钮
   */
  exportExcelButton?:
    | boolean
    | Omit<ExportExcelButtonProps<D, P>, 'baseTableRefObj' | 'columns'>;
  /**
   * 显示折叠展开
   * @default true
   */
  showExpand?: boolean;
}
