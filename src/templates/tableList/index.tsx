import React, {
  useEffect,
  useMemo,
  useState,
  forwardRef,
  useImperativeHandle,
  Ref,
  useRef,
  useCallback,
} from 'react';
import PageHeader from '../../components/pageHeader';
import CardLayout from '../../layouts/cardLayout';
import styled, { css } from 'styled-components';
import BaseTable, { BaseTableRefObj } from '../../components/baseTable';
import { TableListProps, TableListColumnProps } from './Types';
import SearchForm, { SearchFormWrap } from './SearchForm';
import { SizeType } from 'antd/lib/config-provider/SizeContext';
import useFullscreen from 'ahooks/lib/useFullscreen';
import useSelections from 'ahooks/lib/useSelections';
import TableHeader from './TableHeader';
import { getPrefixCls } from '../../_utils';
import ExportExcelButton from './ExportExcelButton';
import { data as kqBasicLayoutData } from '../../layouts/kqBasicLayout';
import { Form } from 'antd';

export default forwardRef(function<D, P>(
  props: TableListProps<D, P>,
  ref: Ref<TableListProps<D, P>>,
) {
  const {
    columns = [],
    getList,
    form = Form.useForm()[0],
    extra,
    tableTitle = '查询表格',
    pageHeaderProps = !kqBasicLayoutData.used,
    showTool = true,
    showColumnsDataIndex,
    onShowColumnsDataIndexChange,
    searchFormProps = {},
    succinct,
    exportExcelButton,
    name,
    showExpand = true,
    needGet: outNeedGet = true,
    ...tableProps
  } = props;
  let { action } = props;
  useImperativeHandle(ref, () => props);
  const [tableSize, setTableSize] = useState<SizeType>();
  const fullscreenRef = useRef<HTMLDivElement>();
  const [isFullscreen, { toggleFull }] = useFullscreen(fullscreenRef);
  const [searchFormValues, setSearchFormValues] = useState<any>({});
  const resultColumns = useMemo<TableListColumnProps<D>[]>(
    () =>
      columns.map(item =>
        item instanceof Function ? item(searchFormValues) : item,
      ),
    [columns, searchFormValues],
  );
  if (searchFormProps?.initialValues) {
    searchFormProps.initialValues = searchFormProps?.initialValues || {};
  }
  resultColumns.forEach(
    ({
      dataIndex,
      searchIndex = dataIndex,
      formItemProps: { initialValue } = {},
    }) => {
      if (initialValue !== undefined && searchIndex) {
        if (!searchFormProps?.initialValues) {
          searchFormProps.initialValues = {};
        }
        searchFormProps.initialValues[searchIndex as any] = initialValue;
      }
    },
  );
  const [listParams, setListParams] = useState<{ [N in keyof P]?: P[N] }>(
    searchFormProps?.initialValues as any,
  );
  const baseTableParams = useMemo(
    () => ({ ...listParams, ...tableProps.params }),
    [listParams, tableProps.params],
  );
  const basicTableColumns = useMemo(
    () =>
      resultColumns
        .filter(
          ({ searchIndex, title, dataIndex }) =>
            !(searchIndex && title) || dataIndex,
        )
        .filter(({ render }) => render !== false),
    [resultColumns],
  );
  const columnsDataIndex = useMemo(
    () =>
      basicTableColumns
        .filter(({ dataIndex }) => dataIndex)
        .map(({ dataIndex }) => dataIndex)
        .flat(2),
    [basicTableColumns],
  );
  const { selected, setSelected, toggle } = useSelections(
    columns.map((_, i) => i + ''),
    showColumnsDataIndex,
  );
  useEffect(() => {
    if (onShowColumnsDataIndexChange) {
      onShowColumnsDataIndexChange(selected);
    }
  }, [onShowColumnsDataIndexChange, selected]);
  useEffect(() => {
    setSelected(columnsDataIndex);
    onShowColumnsDataIndexChange &&
      onShowColumnsDataIndexChange(columnsDataIndex);
  }, [columnsDataIndex, onShowColumnsDataIndexChange, setSelected]);
  const baseTableRef = useRef<BaseTableRefObj<D, P>>();
  action = useMemo(
    () =>
      exportExcelButton ? (
        <>
          <ExportExcelButton
            columns={resultColumns}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            {...exportExcelButton}
            baseTableRefObj={baseTableRef}
          />
          {action}
        </>
      ) : (
        action
      ),
    [action, exportExcelButton, resultColumns],
  );
  const [needGet, setNeedGet] = useState(false);
  useEffect(() => {
    form
      .validateFields()
      .then(() => setNeedGet(true))
      .catch(() => setNeedGet(false));
  }, [form, listParams]);
  return (
    <>
      {pageHeaderProps !== false && !succinct && (
        <PageHeader {...(pageHeaderProps === true ? {} : pageHeaderProps)} />
      )}
      <TableWrap
        succinct={succinct}
        ref={fullscreenRef}
        style={{ background: isFullscreen ? '#fff' : undefined }}
      >
        <SearchForm<D, P>
          form={form}
          columns={resultColumns}
          setListParams={setListParams}
          initListSearchParams={listParams}
          showExpand={showExpand}
          name={name}
          formProps={{
            ...searchFormProps,
            onValuesChange: (_, { ...values }) => {
              setSearchFormValues(values);
              searchFormProps?.onValuesChange?.(_, values);
            },
          }}
        />
        <TableListCard>
          {(!succinct || extra || action) && (
            <TableHeader
              showTool={showTool}
              extra={extra}
              action={action}
              basicTableColumns={basicTableColumns}
              columnsDataIndex={columnsDataIndex}
              fullscreenRef={fullscreenRef}
              isFullscreen={isFullscreen}
              selectedIds={selected}
              selectItem={toggle}
              setSelectedIds={setSelected}
              setTableSize={setTableSize}
              tableSize={tableSize}
              tableTitle={tableTitle}
              toggleFull={toggleFull}
            />
          )}
          <BaseTable<D, P>
            ref={baseTableRef}
            size={tableSize}
            {...tableProps}
            name={name}
            needGet={needGet && outNeedGet}
            columns={useMemo<any>(
              () =>
                basicTableColumns.filter(
                  ({ dataIndex }) =>
                    !dataIndex ||
                    (dataIndex instanceof Array
                      ? dataIndex &&
                        selected.includes(dataIndex[0]) &&
                        selected.includes(dataIndex[1])
                      : selected.includes(dataIndex)),
                ),
              [basicTableColumns, selected],
            )}
            getList={getList}
            params={baseTableParams}
          />
        </TableListCard>
      </TableWrap>
    </>
  );
}) as typeof TableList;

function TableList<D, P>(
  props: TableListProps<D, P> & { ref?: Ref<TableListProps<D, P>> },
) {
  return <TableList {...props} />;
}

const TableWrap = styled.div<{ succinct?: boolean }>`
  .${getPrefixCls}-popover-inner-content {
    max-height: 50vh;
    overflow: auto;
  }
  ${({ succinct }) =>
    succinct &&
    css`
      ${SearchFormWrap} {
        margin: 0 !important;
      }
      ${TableListCard} {
        margin: 0 !important;
      }
    `}
`;

const TableListCard = styled(CardLayout)`
  .${getPrefixCls}-card-body {
    padding: 0;
  }
  .${getPrefixCls}-table-pagination {
    padding: 0 24px;
  }
`;

export * from '../../components/baseTable';
export { default as ExportExcelButton } from './ExportExcelButton';
export * from './Types';
