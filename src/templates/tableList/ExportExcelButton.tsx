// eslint-disable-next-line @typescript-eslint/ban-ts-ignore
// @ts-ignore
import { usePromise } from 'parsec-hooks';
import { BaseTableRefObj, GetListParams } from '../../components/baseTable';
import { useCallback, useMemo } from 'react';
import moment from 'moment';
import AppStore from '../../stores/AppStore';
import useGetBreadcrumbs from '../../components/pageHeader/getBreadcrumbsHooks';
import { Button } from 'antd';
import * as React from 'react';
import { ButtonProps } from 'antd/lib/button/button';
import { saveAs } from 'file-saver';
import { ExportOutlined } from '@ant-design/icons';
import { TableListColumnProps } from './Types';

export interface ExportExcelButtonProps<D, P>
  extends ButtonProps,
    React.RefAttributes<HTMLElement> {
  /**
   * 导出文件的名称
   */
  fileName?: string;
  /**
   * 如果只导出了一条数据，请在getList把total返回出去
   */
  baseTableRefObj: React.MutableRefObject<BaseTableRefObj<D, P>>;
  /**
   * 接口最大分页大小
   * 默认 100
   */
  apiMaxPageSize?: number;
  /**
   * tabList的列
   */
  columns: TableListColumnProps<D>[];
  /**
   * 过滤数据
   */
  listFilter?: (data: D) => boolean;
  /**
   * 是否倒序
   */
  reverseOrder?: boolean;
  /**
   * 单独给导出配置的获取列表接口
   */
  getList?: (
    params: GetListParams<D, { [N in keyof P]?: P[N] }>,
  ) => Promise<{ list?: D[]; total?: number }>;
}

export default <D extends unknown, P extends unknown>({
  fileName,
  baseTableRefObj,
  columns,
  apiMaxPageSize = 100,
  listFilter = () => true,
  reverseOrder = false,
  getList,
  ...props
}: ExportExcelButtonProps<D, P>) => {
  const { routes = useMemo(() => [], []) } = AppStore.useContainer();
  const { currentRouteConfig } = useGetBreadcrumbs('xxx', routes);
  const { handle, loading } = usePromise(
    useCallback(
      params => (getList || baseTableRefObj.current.getList)(params),
      [baseTableRefObj, getList],
    ),
    {
      needInit: false,
    },
  );
  const columnsRender: {
    title?: string;
    dataIndex: string;
    columnWidth?: number;
    render: (text: any, record: D, index: number) => string | number;
  }[] = columns
    .filter(({ title }) => title)
    .filter(
      ({ dataIndex, excelRender = !!dataIndex }) =>
        (dataIndex && excelRender) || excelRender,
    )
    .map(
      (
        {
          excelColumnWidth,
          dataIndex,
          render,
          excelRender = render || (v => v),
          title,
        },
        index,
      ) => ({
        dataIndex:
          dataIndex instanceof Array
            ? dataIndex.join()
            : dataIndex || index + '',
        render: excelRender as any,
        title: title + '',
        columnWidth: excelColumnWidth,
      }),
    );
  return (
    <Button
      loading={loading}
      icon={<ExportOutlined />}
      onClick={async () => {
        const tableOnChangeParams = baseTableRefObj.current.tableOnChangeParams;
        let sheetData: D[] = [];
        let total = 1;
        for (let page = 1; sheetData.length < total; page++) {
          await handle({
            ...tableOnChangeParams,
            pagination: {
              ...tableOnChangeParams.pagination,
              current: page,
              pageSize: apiMaxPageSize,
            },
            // eslint-disable-next-line no-loop-func
          }).then(({ list, total: t }) => {
            if (t === undefined) {
              throw new Error('请在getList把total返回出去');
            }
            total = t || 0;
            sheetData = [...sheetData, ...list];
          });
        }
        sheetData = sheetData.filter(listFilter);
        if (reverseOrder) {
          sheetData = sheetData.reverse();
        }
        const sheetFilter = columnsRender.map(({ dataIndex }) => dataIndex);
        const { default: ExportJsonExcel } = await import('js-export-excel');
        const file = new ExportJsonExcel({
          saveAsBlob: true,
          fileName:
            fileName ||
            `${currentRouteConfig.name}${moment().format(
              'YYYY-MM-DD HH时mm分ss秒',
            )} 总共${sheetData.length}条`,
          datas: [
            {
              sheetData: sheetData.map(data => {
                sheetFilter.forEach((dataIndex, index) => {
                  if (dataIndex.includes(',')) {
                    const arr = dataIndex.split(',');
                    data[dataIndex] = data[arr[0]];
                    columnsRender.push({
                      dataIndex: dataIndex,
                      render: v => {
                        const fn = (key: string, obj: any, index: number) => {
                          if (arr[index + 1]) {
                            return fn(arr[index + 1], obj[key], index + 1);
                          }
                          return obj?.[key] ? obj?.[key] : obj;
                        };
                        return fn(arr[1], v, 1);
                      },
                    });
                  }
                });
                columnsRender?.forEach(({ dataIndex, render }, index) => {
                  data[dataIndex] = render(data[dataIndex], data, index);
                });
                return data;
              }),
              sheetFilter,
              sheetHeader: columnsRender.map(({ title }) => title),
              columnWidths: columnsRender.map(({ columnWidth }) => columnWidth),
            },
          ],
        }).saveExcel();
        saveAs(file);
      }}
      children={'导出'}
      {...props}
    />
  );
};
