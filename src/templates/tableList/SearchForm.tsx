import { <PERSON>, But<PERSON>, Row } from 'antd';
import SearchItem, { SearchItemCol } from './SearchItem';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import LinkButton from '../../components/linkButton';
import RoutesTreeStore from '../../stores/RoutesTreeStore';
import styled from 'styled-components';
import { FormItemProps } from 'antd/lib/form';
import { TableListColumnProps } from './Types';
import { UpOutlined } from '@ant-design/icons';
import { FormInstance } from 'antd/lib/form/hooks/useForm';
import CardLayout from '../../layouts/cardLayout';
import { useInit, useWindowResize } from 'parsec-hooks';
import { FormProps } from 'antd/lib/form';
import { getPrefixCls } from '../../_utils';

export default function<D, P>({
  columns,
  form = Form.useForm()[0],
  setListParams,
  name,
  initListSearchParams,
  formProps,
  showExpand = true,
}: {
  columns: TableListColumnProps<D>[];
  setListParams: (params?: Partial<P>) => void;
  initListSearchParams?: Partial<P>;
  form?: FormInstance;
  name?: string;
  formProps?: FormProps;
  showExpand?: boolean;
}) {
  const [expand, setExpand] = useState(false);
  const searchColumns: typeof columns = useMemo(
    () =>
      [...columns]
        .sort(({ searchSort: a = 999 }, { searchSort: b = 999 }) => a - b)
        .filter(({ search }) => search)
        .map(({ searchIndex, dataIndex, ...item }) => ({
          ...item,
          dataIndex: (searchIndex as any) || dataIndex,
        })),
    [columns],
  );
  const {
    reloadList,
    resetFormRef,
    reloadState,
  } = RoutesTreeStore.useContainer();
  useEffect(() => {
    if (resetFormRef.current) {
      form.resetFields();
      resetFormRef.current = false;
    }
  }, [form, reloadState, resetFormRef]);

  const [listSearchParams, setListSearchParams] = useState(
    initListSearchParams,
  );
  const formInitialValuesRef = useRef({});
  const onValuesChange = useCallback(
    (_, { ...values }) => {
      searchColumns.forEach(({ dataIndex }) => {
        if (dataIndex instanceof Array) {
          const [value1, value2] = values[dataIndex[0]] || [];
          if (value1) {
            values[dataIndex[0]] = value1;
          }
          if (value2) {
            values[dataIndex[1]] = value2;
          }
        }
      });
      setListSearchParams(values as P);
      formProps?.onValuesChange(_, values);
    },
    [formProps, searchColumns],
  );
  useInit(() => {
    const arrSearchIndex = searchColumns
      .filter(
        ({ searchIndex, dataIndex }) =>
          searchIndex instanceof Array || dataIndex instanceof Array,
      )
      .map(({ searchIndex, dataIndex }) => searchIndex || dataIndex) as [
      string,
      string,
    ][];
    const values = { ...formProps?.initialValues };
    arrSearchIndex.forEach(([index1, index2]) => {
      if (values[index1]) {
        values[index1] = [values[index1], values[index2]];
        delete values[index2];
      }
    });
    formInitialValuesRef.current = values;
    form.setFieldsValue(values);
    const oldSet = form.setFieldsValue;
    form.setFieldsValue = values => {
      oldSet(values);
      onValuesChange(undefined, { ...form.getFieldsValue(), ...values });
    };
  });

  const { clientWidth } = useWindowResize();
  const ExpandBtns = useMemo(
    () => (
      <SearchItemCol
        className={'tableList-search-form-btns'}
        style={{ maxWidth: 'none', flex: 1 }}
      >
        <FormItem className={'expand'}>
          <Row
            justify={expand ? 'end' : 'start'}
            align={'middle'}
            style={{
              lineHeight: '32px',
              whiteSpace: 'nowrap',
              justifyContent: 'flex-end',
            }}
          >
            <Button
              htmlType={'submit'}
              type={'primary'}
              onClick={() => {
                form.validateFields().then(() => {
                  setListParams(listSearchParams);
                  reloadList(name);
                });
              }}
            >
              查询
            </Button>
            <MarginButton
              onClick={(e: any) => {
                form.resetFields();
                const value = (formProps?.initialValues || {}) as P;
                form.setFieldsValue(formInitialValuesRef.current);
                setListParams(value);
                setListSearchParams(value);
                formProps?.onReset?.(e);
              }}
            >
              重置
            </MarginButton>
            {showExpand &&
              searchColumns.length >=
                4 - (clientWidth < 990 ? 2 : clientWidth < 1600 ? 1 : 0) && (
                <LinkButton
                  onClick={e => {
                    e.preventDefault();
                    setExpand(!expand);
                  }}
                >
                  {expand ? '收起' : '展开'}{' '}
                  <UpOutlined
                    style={{
                      transform: `rotate(${expand ? 0 : -180}deg)`,
                      transition: 'transform .3s',
                    }}
                  />
                </LinkButton>
              )}
          </Row>
        </FormItem>
      </SearchItemCol>
    ),
    [
      clientWidth,
      expand,
      form,
      formProps,
      listSearchParams,
      name,
      reloadList,
      searchColumns.length,
      setListParams,
      showExpand,
    ],
  );

  const searchItems = useMemo(
    () =>
      searchColumns.map(
        ({ colProps, title, search, formItemProps, dataIndex }, index) => (
          <SearchItem<P>
            show={!showExpand || expand || index <= 2}
            className={!expand && showExpand ? `SearchItem${index}` : undefined}
            key={dataIndex as string}
            search={search}
            colProps={colProps}
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            title={title}
            dataIndex={dataIndex as any}
            formItemProps={formItemProps}
          />
        ),
      ),
    [expand, searchColumns, showExpand],
  );

  return searchColumns.length ? (
    <SearchFormWrap className={'tableList-search-wrap'}>
      <Form
        {...formProps}
        className={'tableList-search-form'}
        form={form}
        style={{ display: 'flex' }}
        layout={'inline'}
        onValuesChange={onValuesChange}
        onFinish={() => {
          form.validateFields().then(values => {
            setListParams(listSearchParams);
            reloadList(name);
            formProps?.onFinish?.(values);
          });
        }}
      >
        {searchItems}
        {ExpandBtns}
      </Form>
    </SearchFormWrap>
  ) : null;
}

export const SearchFormWrap = styled(CardLayout)`
  .${getPrefixCls}-card-body {
    padding: 24px 16px 0;
  }
`;

const FormItem = styled(Form.Item)<FormItemProps>`
  &&& {
    display: flex;
    margin-right: 0;
    margin-bottom: 24px;
    @media (max-width: 575px) {
      margin-bottom: 4px;
    }
    .${getPrefixCls}-form-item-control-wrapper {
      flex: 1;
    }
    .${getPrefixCls}-form-item-row {
      flex: 1;
    }
  }
`;
const MarginButton = styled(Button)`
  margin: 0 8px;
`;
