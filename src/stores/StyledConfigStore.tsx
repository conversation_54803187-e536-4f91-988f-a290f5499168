import { ThemeProvider } from 'styled-components';
import React, { PropsWithChildren, useEffect } from 'react';
import { message, ConfigProvider, notification } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { appData } from '../app';

export interface StyledConfig {
  prefixCls?: string;
}

export default ({ children, ...theme }: PropsWithChildren<StyledConfig>) => {
  useEffect(() => {
    appData.storeValue.prefixCls = theme.prefixCls;
    const options = { prefixCls: `${theme.prefixCls || 'ant'}-message` };
    message.config(options);
    const notificationOptions = {
      prefixCls: `${theme.prefixCls || 'ant'}-notification`,
    };
    notification.config(notificationOptions);
  }, [theme.prefixCls]);
  return (
    <ThemeProvider theme={theme}>
      <ConfigProvider locale={zhCN} prefixCls={theme.prefixCls}>
        {children}
      </ConfigProvider>
    </ThemeProvider>
  );
};
