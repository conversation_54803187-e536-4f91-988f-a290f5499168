import { useCallback, useRef, useState } from 'react';
import { useRefState, createContainer } from 'parsec-hooks';
import { appData } from '../app';

export default createContainer(
  (v: any) => {
    const [hideRoutesKey, setHideRoutesKey, hideRoutesKeyRef] = useRefState<
      string[]
    >([]);
    const hideRoute = useCallback(
      (routeKey: string) => {
        const newRoutes = [...(hideRoutesKeyRef.current || [])];
        const index = newRoutes.indexOf(routeKey);
        if (index === -1) {
          newRoutes.push(routeKey);
        }
        setHideRoutesKey(newRoutes);
      },
      [hideRoutesKeyRef, setHideRoutesKey],
    );
    const showRoute = useCallback(
      (routeKey: string) => {
        const newRoutes = [...(hideRoutesKeyRef.current || [])];
        const index = newRoutes.indexOf(routeKey);
        if (index !== -1) {
          newRoutes.splice(index, 1);
        }
        hideRoutesKeyRef.current = newRoutes;
        setHideRoutesKey(newRoutes);
      },
      [hideRoutesKeyRef, setHideRoutesKey],
    );

    const [reloadState, setReloadState] = useState(false);
    const [reloadListName, setReloadListName] = useState('');
    const resetFormRef = useRef(false);
    const reloadList = useCallback(
      (name?: string | 'all', resetForm = false) => {
        resetFormRef.current = resetForm;
        setReloadState(!reloadState);
        if (name) {
          setReloadListName(name);
        }
      },
      [reloadState],
    );

    appData.storeValue.reloadTableList = reloadList;

    return (
      v || {
        hideRoutesKey,
        hideRoute,
        hideRoutesKeyRef,
        showRoute,
        reloadList,
        reloadState,
        reloadListName,
        setReloadListName,
        resetFormRef,
      }
    );
  },
  { reloadState: 0, reloadListName: '' },
);
