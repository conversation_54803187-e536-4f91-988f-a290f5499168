import { createContainer } from 'parsec-hooks';
import { appData } from '../app';
import { RouteProps } from 'react-router-dom';
import { RouteConfig } from '../types';
import { History } from 'history';

export interface SiteConfig {
  /**
   * 额外的路由，不包含在基本布局里的路由，比如登录页
   */
  aloneRoutes: (RouteProps &
    Pick<RouteConfig, 'microAppName' | 'microAppUrl'>)[];
  /**
   * 路由
   */
  routes: RouteConfig[];
  /**
   * 优先使用permissions进行鉴权，传入routesId后会根据这个进行过滤
   */
  routeIds?: (number | string)[];
  logo?: string;
  /**
   * 版权，为false则不显示
   */
  copyright?: React.ReactNode | false;
  /**
   * 名称
   */
  name?: React.ReactNode;
  /**
   * 侧边栏的名称，默认为上面name字段
   */
  slidName?: React.ReactNode;
  /**
   * 登录界面的描述，为false则不显示
   */
  description?: React.ReactNode | false;
  /**
   * 整体布局
   */
  basicLayout: React.ReactNode;
  /**
   * 路由和按钮 权限控制
   * @param config
   */
  permissions?: (
    params: {
      permissionsId: string | number | string[] | number[];
    } & RouteConfig,
  ) => boolean;
  /**
   * 登录地址，默认/login
   */
  loginPath?: string;
  /**
   * 有效登录判断，默认检查本地是否有token
   */
  checkLogin?: (pathname: string) => boolean;
  /**
   * router的basename，用于设置二级目录
   */
  basename?: string;
  /**
   * 根据接口url处理登录失效
   */
  handleLogonFailure?: (url: string) => void;
  /**
   * 刷新table列表，保留字段，不要传进来
   */
  reloadTableList: (tableName?: string, resetForm?: boolean) => void;
  /**
   * 自定义History，可以用这个定义创建Hash路由还是Browser路由
   */
  history?: History;
  /**
   * 操作确认的提示模版，false则不需要
   * 默认为：'确定要{{text}}吗？'
   */
  confirmTemplate?: string | false;
}

export default createContainer((config: SiteConfig) => {
  appData.storeValue = { ...appData.storeValue, ...config };
  return config;
});
