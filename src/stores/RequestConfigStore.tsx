import { createContainer } from 'parsec-hooks';
import { appData } from '../app';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { History } from 'history';

export interface RequestConfig {
  /**
   * 请求的apiHost
   */
  apiHost?: string | ((apiPath: string) => string);
  /**
   * storage必须能操作token，用于请求
   * 不传的话会影响鉴权
   */
  storage?: {
    set: (key: 'token', value: string) => void;
    get: (key: 'token') => string | null;
    del: (key: 'token') => void;
  };
  /**
   * 根据接口url返回指定token
   */
  specifiedToken?: (url: string) => string | null;
  /**
   * 根据接口url设置token
   */
  accordingUrlSetToken?: (url: string, token: string) => void;
  /**
   * 请求拦截
   */
  requestInterceptor?: (
    config: AxiosRequestConfig,
  ) => AxiosRequestConfig | Promise<AxiosRequestConfig>;
  /**
   * 返回拦截
   */
  responseInterceptor?: (response: AxiosResponse) => AxiosResponse;
  /**
   * token失效跳转，默认跳loginPath
   */
  tokenInvalidJump?: (history: History) => void;
  /**
   * 网络错误的通知持续时间
   */
  errMsgDuration?: number;
  /**
   * 自定义显示网络错误通知
   */
  networkErrorNotification?: (data: {
    description: string;
    status: number;
    duration?: number;
  }) => void;
  /**
   * 接口报403直接显示登录，并同时不会跳到登录页面
   */
  noJumpLogin?: boolean;
  /**
   * 自动带上Authorization头
   * @default true
   */
  authorizationHeader?: boolean;
}

export default createContainer((config: RequestConfig) => {
  appData.storeValue = { ...appData.storeValue, ...config };
  return config;
});
