import { createContainer } from 'parsec-hooks';
import RequestConfigStore, { RequestConfig } from './RequestConfigStore';
import UploadConfigStore, { UploadConfig } from './UploadConfigStore';
import SiteConfigStore, { SiteConfig } from './SiteConfigStore';
import { StyledConfig } from './StyledConfigStore';

export interface AppStoreValue
  extends SiteConfig,
    RequestConfig,
    UploadConfig,
    StyledConfig {}

export default createContainer((initialState: SiteConfig) => {
  const requestConfig = RequestConfigStore.useContainer();
  const uploadConfig = UploadConfigStore.useContainer();
  const siteConfig = SiteConfigStore.useContainer();
  return {
    ...requestConfig,
    ...uploadConfig,
    ...initialState,
    ...siteConfig,
  };
}, {} as any);
