import { createContainer } from 'parsec-hooks';
import { appData } from '../app';

export interface UploadConfig {
  /**
   * 上传方法
   * @param file
   */
  uploadFn?: (file: File) => Promise<string>;
  /**
   * upload组件header是否带token
   */
  uploadWithToken?: false;
}

export default createContainer((config: UploadConfig) => {
  appData.storeValue = { ...appData.storeValue, ...config };
  return config;
});
