# 详情布局

基于 Antd Pro [基础详情页](https://preview.pro.ant.design/profile/basic)样式的详情页布局。

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { DetailLayout, FormDescriptions, LinkButton } from 'parsec-admin';
import { Table } from 'antd';

export default () => {
  return (
    <DetailLayout
      headerProps={{
        title: '基础详情页',
      }}
      cardsProps={[
        {
          title: '退款申请',
          children: (
            <FormDescriptions
              items={[
                {
                  label: '取货单号',
                  children: '1000000000',
                },
                {
                  label: '状态',
                  children: '已取货',
                },
                {
                  label: '销售单号',
                  children: '1234123421',
                },
                {
                  label: '子订单',
                  children: '3214321432',
                },
              ]}
            />
          ),
        },
        {
          title: '用户信息',
          children: (
            <FormDescriptions
              items={[
                {
                  label: '用户姓名',
                  children: '付小小',
                },
                {
                  label: '联系电话',
                  children: '18100000000',
                },
                {
                  label: '常用快递',
                  children: '菜鸟仓储',
                },
                {
                  label: '取货地址',
                  children: '浙江省杭州市西湖区万塘路18号',
                },
                {
                  label: '备注',
                  children: '无',
                },
              ]}
            />
          ),
        },
        {
          title: '退货商品',
          children: (
            <Table
              pagination={false}
              columns={[
                {
                  title: '商品编号',
                  dataIndex: 'id',
                  render: v => <LinkButton>{v}</LinkButton>,
                },
                { title: '商品名称', dataIndex: 'name' },
                { title: '商品条码', dataIndex: 'code' },
                { title: '单价', dataIndex: 'price', align: 'right' },
                { title: '数量（件）', dataIndex: 'num', align: 'right' },
                { title: '金额', dataIndex: 'money', align: 'right' },
              ]}
              rowKey={'id'}
              dataSource={[
                {
                  id: '1234561',
                  name: '矿泉水 550ml',
                  code: '12421432143214321',
                  price: '2.00',
                  num: '1',
                  money: '2.00',
                },
                {
                  id: '1234516',
                  name: '凉茶 300ml',
                  code: '12421432143214321',
                  price: '2.00',
                  num: '1',
                  money: '2.00',
                },
              ]}
            />
          ),
        },
      ]}
    />
  );
};
```

## API

> 更多用法可以查看 [Card](https://ant.design/components/card-cn/#API) 。
