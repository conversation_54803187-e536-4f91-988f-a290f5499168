import React from 'react';
import { Card } from 'antd';
import { CardProps } from 'antd/lib/card';
import { BlankSection, BlankLayoutProps, BlankLayoutSection } from '../index';
import styled from 'styled-components';

interface DetailLayoutProps extends BlankLayoutProps, DetailCardSectionProps {}

export default ({
  cardsProps,
  headerProps,
  spinProps,
  ...props
}: DetailLayoutProps) => {
  return (
    <BlankLayoutSection headerProps={headerProps} spinProps={spinProps}>
      <MyBlankSection
        {...props}
        children={<DetailCardSection cardsProps={cardsProps} />}
      />
    </BlankLayoutSection>
  );
};

const MyBlankSection = styled(BlankSection)`
  padding: 0;
`;

export interface DetailCardSectionProps {
  cardsProps: CardProps[];
}

export const DetailCardSection = ({ cardsProps }: DetailCardSectionProps) => {
  return (
    <>
      {cardsProps.map((props, index) => (
        <Card bordered={false} {...props} key={index} />
      ))}
    </>
  );
};
