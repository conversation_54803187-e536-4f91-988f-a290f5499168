import React from 'react';
import { Layout } from 'antd';
import AppStore from '../../stores/AppStore';
import './index.less';
import bg from './bg.png';
const { Footer, Content } = Layout;

export default ({ children }: { children: React.ReactNode }) => {
  const {
    name = '凯桥信息',
    copyright = (
      <>
        <p>Copyright © 重庆凯桥信息技术有限公司</p>
        <p>备案号:渝ICP备12001070号</p>
      </>
    ),
  } = AppStore.useContainer();
  return (
    <div className="page-login" style={{ backgroundImage: `url(${bg})` }}>
      <Content>
        <div className="logo-box">
          <div className="title">欢迎使用{name}</div>
          <div className={'logo-form'}>{children}</div>
        </div>
      </Content>
      <Footer>{copyright}</Footer>
    </div>
  );
};
