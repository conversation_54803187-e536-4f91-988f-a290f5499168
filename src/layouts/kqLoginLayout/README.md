# 凯桥登录布局

使用的时候需要在`app.tsx`引入`import 'parsec-admin/lib/layouts/kqBasicLayout/theme.less'`。

```tsx
import React from 'react';
import { Form, Input, Tabs, Button, Checkbox } from 'antd';
import { KqLoginLayout } from 'parsec-admin';
import styled from 'styled-components';
import { TeamOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
// import 'parsec-admin/lib/layouts/kqBasicLayout/theme.less';

const { Password } = Input;

export default () => {
  return (
    <KqLoginLayout>
      <Form onFinish={console.log}>
        <Form.Item
          name={'username'}
          rules={[
            {
              required: true,
              message: '请输入用户名',
            },
          ]}
        >
          <Input
            size={'large'}
            placeholder={'用户名'}
            suffix={<TeamOutlined />}
          />
        </Form.Item>
        <Form.Item
          name={'password'}
          rules={[
            {
              required: true,
              message: '请输入密码',
            },
          ]}
        >
          <Password
            size={'large'}
            placeholder={'密码'}
            type={'password'}
            suffix={<LockOutlined />}
          />
        </Form.Item>
        <Form.Item
          name={'captcha'}
          rules={[
            {
              required: true,
              message: '请输入验证码',
            },
          ]}
        >
          <Input
            size={'large'}
            placeholder={'验证码'}
            suffix={<MailOutlined />}
          />
        </Form.Item>
        <Form.Item name="remember" valuePropName={'checked'}>
          <Checkbox>记住账号和密码</Checkbox>
        </Form.Item>
        <Form.Item style={{ textAlign: 'center' }}>
          <Button
            shape={'round'}
            size={'large'}
            block
            type={'primary'}
            htmlType={'submit'}
            style={{
              border: 'none',
              fontWeight: 500,
              width: 212,
              fontSize: 24,
              height: 50,
              backgroundColor: '#2059ff',
            }}
          >
            登录
          </Button>
        </Form.Item>
      </Form>
    </KqLoginLayout>
  );
};
```
