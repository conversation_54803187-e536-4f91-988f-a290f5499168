# 卡片布局

基于 Antd Pro [高级表单](https://preview.pro.ant.design/form/advanced-form)样式的卡片布局。

```tsx
/**
 * background: '#f6f7f9'
 */
import React, { useMemo } from 'react';
import {
  BlankLayout,
  CardLayout,
  FormDescriptions,
  ArrSelect,
  DayRangePicker,
  DateShow,
  PageHeader,
} from 'parsec-admin';
import { Input } from 'antd';

export default () => {
  const data = useMemo(
    () => ({
      name: '仓库名',
      domainName: '仓库域名',
      controller: '小吴',
      approver: '小红',
      startAt: '2020-5-20',
      endAt: '2020-5-21',
      type: '私密',
    }),
    [],
  );
  return (
    <>
      <PageHeader title={'高级表单'}>
        高级表单常见于一次性输入和提交大批量数据的场景。
      </PageHeader>
      <CardLayout title={'仓库管理'}>
        <FormDescriptions
          data={data}
          edit
          items={[
            {
              label: '仓库名',
              name: 'name',
              required: true,
              formItemProps: {
                render: <Input placeholder={'请输入仓库名称'} />,
              },
            },
            {
              label: '仓库域名',
              name: 'domainName',
              required: true,
              formItemProps: {
                render: (
                  <Input
                    placeholder={'请输入'}
                    addonBefore="http://"
                    addonAfter=".com"
                  />
                ),
              },
            },
            {
              label: '仓库管理员',
              name: 'controller',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={['小吴', '小红']}
                    placeholder={'请选择仓库管理员'}
                  />
                ),
              },
            },
            {
              label: '审批人',
              name: 'approver',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={['小吴', '小红']}
                    placeholder={'请选择审批人'}
                  />
                ),
              },
            },
            {
              label: '生效日期',
              name: ['startAt', 'endAt'],
              required: true,
              formItemProps: {
                render: <DayRangePicker />,
              },
              render: ([date1, date2]) => (
                <>
                  <DateShow>{date1}</DateShow> - <DateShow>{date2}</DateShow>
                </>
              ),
            },
            {
              label: '仓库类型',
              name: 'type',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={['私密', '公开']}
                    placeholder={'请选择仓库类型'}
                  />
                ),
              },
            },
          ]}
        />
      </CardLayout>
      <CardLayout title={'仓库管理'}>
        <FormDescriptions
          data={data}
          edit
          items={[
            {
              label: '仓库名',
              name: 'name',
              required: true,
              formItemProps: {
                render: <Input placeholder={'请输入仓库名称'} />,
              },
            },
            {
              label: '仓库域名',
              name: 'domainName',
              required: true,
              formItemProps: {
                render: (
                  <Input
                    placeholder={'请输入'}
                    addonBefore="http://"
                    addonAfter=".com"
                  />
                ),
              },
            },
            {
              label: '仓库管理员',
              name: 'controller',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={['小吴', '小红']}
                    placeholder={'请选择仓库管理员'}
                  />
                ),
              },
            },
            {
              label: '审批人',
              name: 'approver',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={['小吴', '小红']}
                    placeholder={'请选择审批人'}
                  />
                ),
              },
            },
            {
              label: '生效日期',
              name: ['startAt', 'endAt'],
              required: true,
              formItemProps: {
                render: <DayRangePicker />,
              },
              render: ([date1, date2]) => (
                <>
                  <DateShow>{date1}</DateShow> - <DateShow>{date2}</DateShow>
                </>
              ),
            },
            {
              label: '仓库类型',
              name: 'type',
              required: true,
              formItemProps: {
                render: (
                  <ArrSelect
                    options={['私密', '公开']}
                    placeholder={'请选择仓库类型'}
                  />
                ),
              },
            },
          ]}
        />
      </CardLayout>
    </>
  );
};
```

## API

> 更多用法可以查看 [Card](https://ant.design/components/card-cn/#API) 。
