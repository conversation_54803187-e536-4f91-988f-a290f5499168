# 空白块布局

基础的空白块布局，包含页头等统一的基本样式。

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { BlankLayout } from 'parsec-admin';

export default () => <BlankLayout>hello !!</BlankLayout>;
```

## 自定义页头

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { BlankLayout } from 'parsec-admin';
import { Button, Descriptions } from 'antd';

export default () => (
  <BlankLayout
    headerProps={{
      onBack: () => window.history.back(),
      title: 'Title',
      subTitle: 'This is a subtitle',
      extra: [
        <Button key="3">Operation</Button>,
        <Button key="2">Operation</Button>,
        <Button key="1" type="primary">
          Primary
        </Button>,
      ],
      children: (
        <Descriptions size="small" column={3}>
          <Descriptions.Item label="Created"><PERSON>i <PERSON></Descriptions.Item>
          <Descriptions.Item label="Association">
            <a>421421</a>
          </Descriptions.Item>
          <Descriptions.Item label="Creation Time">
            2017-01-10
          </Descriptions.Item>
          <Descriptions.Item label="Effective Time">
            2017-10-10
          </Descriptions.Item>
          <Descriptions.Item label="Remarks">
            Gonghu Road, Xihu District, Hangzhou, Zhejiang, China
          </Descriptions.Item>
        </Descriptions>
      ),
    }}
  >
    hello !!
  </BlankLayout>
);
```

## 设置加载状态

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { BlankLayout } from 'parsec-admin';
import { Button, Descriptions } from 'antd';

export default () => (
  <BlankLayout
    spinProps={{ spinning: true }}
    headerProps={{
      onBack: () => window.history.back(),
      title: 'Title',
      subTitle: 'This is a subtitle',
      extra: [
        <Button key="3">Operation</Button>,
        <Button key="2">Operation</Button>,
        <Button key="1" type="primary">
          Primary
        </Button>,
      ],
      children: (
        <Descriptions size="small" column={3}>
          <Descriptions.Item label="Created">Lili Qu</Descriptions.Item>
          <Descriptions.Item label="Association">
            <a>421421</a>
          </Descriptions.Item>
          <Descriptions.Item label="Creation Time">
            2017-01-10
          </Descriptions.Item>
          <Descriptions.Item label="Effective Time">
            2017-10-10
          </Descriptions.Item>
          <Descriptions.Item label="Remarks">
            Gonghu Road, Xihu District, Hangzhou, Zhejiang, China
          </Descriptions.Item>
        </Descriptions>
      ),
    }}
  >
    hello !!
  </BlankLayout>
);
```

## API

| 属性        | 说明                                                                                          | 默认值 |
| ----------- | --------------------------------------------------------------------------------------------- | ------ |
| headerProps | 自定义[页头 props](https://ant.design/components/pagination-cn/#API) ，`false` 则不显示页头。 | -      |
| sideProps   | 自定义 [Spin props](https://ant.design/components/spin-cn/#API) 。                            | -      |
