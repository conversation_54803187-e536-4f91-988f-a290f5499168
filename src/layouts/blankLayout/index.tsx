import React from 'react';
import styled from 'styled-components';
import { Spin } from 'antd';
import { SpinProps } from 'antd/lib/spin';
import MyPageHeader, { MyPageHeaderProps } from '../../components/pageHeader';
import { data as kqBasicLayoutData } from '../kqBasicLayout';

export interface BlankLayoutSectionProps {
  headerProps?: MyPageHeaderProps | false;
  spinProps?: SpinProps;
  children?: React.ReactNode;
}

export interface BlankLayoutProps
  extends React.DetailsHTMLAttributes<HTMLDivElement>,
    BlankLayoutSectionProps {}

export const BlankLayoutSection = ({
  headerProps = kqBasicLayoutData.used ? false : undefined,
  spinProps,
  children,
}: BlankLayoutSectionProps) => {
  return (
    <Spin spinning={false} {...spinProps}>
      {headerProps !== false && <MyPageHeader {...headerProps} />}
      {children}
    </Spin>
  );
};

export default ({ children, ...props }: BlankLayoutProps) => (
  <BlankLayoutSection {...props}>
    <BlankSection children={children} {...props} />
  </BlankLayoutSection>
);

export const BlankSection = styled.section`
  background-color: var(--component-background);
  padding: 24px;
  margin: 24px;
`;
