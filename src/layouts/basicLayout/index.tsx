import React, { useMemo } from 'react';
import { Layout, Drawer } from 'antd';
import styled from 'styled-components';
import Footer from '../../components/footer';
import Content from './Content';
import { MenuProps } from './Menu';
import AppStore from '../../stores/AppStore';
import { RouteConfig } from '../../types';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import useCustomLayout from './customLayoutHooks';
import Side, { MySideProps } from './Side';
import { getPrefixCls } from '../../_utils';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
const isMainApp = window.__POWERED_BY_QIANKUN__;

const { Header } = Layout;

export interface Props {
  /**
   * 布局头部内容
   */
  header?: React.ReactNode;
  /**
   * 可自定义侧边头部
   */
  renderSideHeader?: (collapsed: boolean) => React.ReactNode;
  /**
   * 可自定义侧边
   * @param menu
   * @param states
   */
  renderSide?: (
    menu: React.FC<MenuProps>,
    states: {
      setCollapsed: (collapsed: boolean) => void;
      collapsed: boolean;
      isMobile: boolean;
      drawerVisible: boolean;
    },
  ) => React.ReactNode;
  /**
   * 自定义路由
   */
  routes?: RouteConfig[];
  menuProps?: Omit<MenuProps, 'selectedKey' | 'children'>;
  sideProps?: Omit<MySideProps, 'menuProps' | 'setMenuCollapsed'>;
}

export default ({
  header,
  renderSide,
  renderSideHeader,
  routes,
  menuProps,
  sideProps,
}: Props) => {
  const {
    menuCollapsed,
    isMobile,
    drawerVisible,
    setDrawerVisible,
    setMenuCollapsed,
    menuOpenKeys,
    menuOnOpenChange,
    switchCollapsed,
    contentRoutes,
    menuNode,
    menuSelectedKey,
  } = useCustomLayout(routes);

  const appValue = AppStore.useContainer();
  const { logo, name = 'Ant Design Pro', slidName = name } = appValue;
  const side = useMemo(
    () => (
      <Side
        menuCollapsed={!isMobile && menuCollapsed}
        renderSide={renderSide}
        setMenuCollapsed={setMenuCollapsed}
        header={
          renderSideHeader ? (
            renderSideHeader(isMobile || !menuCollapsed)
          ) : (
            <SiderLogo>
              {logo && <img src={logo} alt="" />}
              {(isMobile || !menuCollapsed) && <h1>{slidName}</h1>}
            </SiderLogo>
          )
        }
        menuProps={{
          children: menuNode,
          selectedKey: menuSelectedKey,
          openKeys: menuOpenKeys,
          onOpenChange: menuOnOpenChange,
          ...menuProps,
        }}
        {...sideProps}
      />
    ),
    [
      isMobile,
      menuCollapsed,
      renderSide,
      setMenuCollapsed,
      renderSideHeader,
      logo,
      slidName,
      menuNode,
      menuSelectedKey,
      menuOpenKeys,
      menuOnOpenChange,
      menuProps,
      sideProps,
    ],
  );

  return window.__POWERED_BY_QIANKUN__ && isMainApp ? (
    contentRoutes
  ) : (
    <MyLayout>
      {isMobile ? (
        <MyDrawer
          placement={'left'}
          closable={false}
          onClose={() => {
            setDrawerVisible(!drawerVisible);
            switchCollapsed(!menuCollapsed);
          }}
          visible={drawerVisible}
        >
          {side}
        </MyDrawer>
      ) : (
        side
      )}
      <InnerLayout style={{ width: isMobile ? '100%' : 0 }}>
        <MyHeader>
          <HeaderLeft>
            {isMobile && logo && <HeaderLogin src={logo} alt="" />}
            <TriggerIcon
              style={{ cursor: 'pointer' }}
              onClick={() => {
                switchCollapsed(!menuCollapsed);
                setDrawerVisible(!drawerVisible);
              }}
            >
              {!menuCollapsed ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
            </TriggerIcon>
          </HeaderLeft>
          {header}
        </MyHeader>
        <Content>{contentRoutes}</Content>
        <Footer />
      </InnerLayout>
    </MyLayout>
  );
};

const InnerLayout = styled(Layout)`
  &&& {
    overflow: initial;
    flex: 1;
  }
`;
const HeaderLogin = styled.img`
  width: 32px;
  margin-left: 24px;
`;
const SiderLogo = styled.div`
  padding-left: 24px;
  display: flex;
  align-items: center;
  height: 64px;
  box-sizing: border-box;
  img {
    width: 32px;
    margin-right: 12px;
  }
  h1 {
    color: #fff;
    margin: 0;
    font-size: 20px;
    vertical-align: middle;
    min-width: 178px;
    max-width: 178px;
    font-weight: 600;
  }
`;
const TriggerIcon = styled.span`
  .anticon {
    font-size: 20px;
    height: 64px;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;
const MyLayout = styled(Layout)`
  min-height: 100vh;
`;
const MyHeader = styled(Header)`
  && {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    background-color: var(--component-background);
    z-index: 4;
  }
`;
const HeaderLeft = styled.div`
  display: flex;
  height: 100%;
  align-items: center;
`;
const MyDrawer = styled(Drawer)`
  .${getPrefixCls}-drawer-body {
    padding: 0;
    height: 100vh;
  }
`;
