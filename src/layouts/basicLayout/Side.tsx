import React, { useMemo } from 'react';
import Menu, { MenuProps } from './Menu';
import styled from 'styled-components';
import { Layout } from 'antd';
import { SiderProps } from 'antd/lib/layout/Sider';

const { Sider } = Layout;

export interface MySideProps extends SiderProps {
  isMobile?: boolean;
  menuCollapsed?: boolean;
  renderSide?: (
    menu: React.FC<MenuProps>,
    states: {
      setCollapsed: (collapsed: boolean) => void;
      collapsed: boolean;
      isMobile: boolean;
      drawerVisible: boolean;
    },
  ) => React.ReactNode;
  drawerVisible?: boolean;
  setMenuCollapsed: (collapsed: boolean) => void;
  header?: React.ReactNode;
  menuProps: MenuProps;
}

export default ({
  isMobile = false,
  menuCollapsed = false,
  drawerVisible = false,
  renderSide,
  setMenuCollapsed,
  header,
  menuProps,
  ...props
}: MySideProps) => {
  return useMemo(
    () => (
      <MySider
        collapsible={isMobile}
        collapsed={!isMobile && menuCollapsed}
        width={256}
        trigger={null}
        {...props}
      >
        {renderSide ? (
          renderSide(Menu, {
            isMobile,
            drawerVisible,
            collapsed: menuCollapsed,
            setCollapsed: setMenuCollapsed,
          })
        ) : (
          <>
            {header}
            <Menu {...menuProps} />
          </>
        )}
      </MySider>
    ),
    [
      props,
      isMobile,
      menuCollapsed,
      renderSide,
      drawerVisible,
      setMenuCollapsed,
      header,
      menuProps,
    ],
  );
};

const MySider = styled(Sider)`
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  z-index: 5;
  height: 100vh;
  position: sticky !important;
  top: 0;
  .ant-layout-sider-children {
    overflow: auto;
  }
`;
