import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useRefState, useDomReSize, useGlobalState } from 'parsec-hooks';
import useGetContentRoutes from './getContentRoutesHooks';
import { RouteConfig } from '../../types';
import AppStore from '../../stores/AppStore';
import RoutesTreeStore from '../../stores/RoutesTreeStore';
import useGetMenuNode from './getMenuNodeHooks';
import useGetMenuRoutes from './getMenuRoutesHooks';
import wrapRoutes from '../../utils/wrapRoutes';
import useGetBreadcrumbs from '../../components/pageHeader/getBreadcrumbsHooks';
import { useLocation } from 'react-router-dom';

export default (routes: RouteConfig[] = AppStore.useContainer().routes) => {
  const { routeIds, permissions } = AppStore.useContainer();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [menuOpenKeys, setMenuOpenKeys, menuOpenKeysRef] = useRefState<
    string[]
  >([]);
  const preMenuOpenKeys = useRef(menuOpenKeys);
  const [menuCollapsed, setMenuCollapsed] = useGlobalState<boolean>(
    'menuCollapsed',
    false,
  );
  const switchCollapsed = useCallback(
    (collapsed: boolean) => {
      if (collapsed && menuOpenKeysRef.current) {
        preMenuOpenKeys.current = [...menuOpenKeysRef.current];
        setMenuOpenKeys([]);
      } else {
        setMenuOpenKeys(preMenuOpenKeys.current);
      }
      setMenuCollapsed(collapsed);
    },
    [menuOpenKeysRef, setMenuCollapsed, setMenuOpenKeys],
  );
  const documentRef = useRef(document.documentElement);
  const { width } = useDomReSize(documentRef);
  useEffect(() => {
    setMenuCollapsed(width <= 1400);
  }, [setMenuCollapsed, width]);
  const isMobile = width <= 600;
  const { contentRoutes, homePath } = useGetContentRoutes(routes);
  const { hideRoutesKey } = RoutesTreeStore.useContainer();
  const wrappedRoutes = useMemo(() => {
    wrapRoutes(routes || []);
    return routes;
  }, [routes]);
  const { pathname } = useLocation();
  const { routes: menuRoutes, selectedKey: menuSelectedKey } = useGetMenuRoutes(
    pathname,
    {
      routeIds,
      routes: wrappedRoutes,
      permissions,
    },
  );
  const menuNode = useGetMenuNode(menuRoutes);
  return {
    ...useGetBreadcrumbs(homePath, routes),
    menuRoutes,
    isMobile,
    drawerVisible,
    setDrawerVisible,
    switchCollapsed,
    contentRoutes,
    isDetailPage: useMemo(() => !!hideRoutesKey.length, [hideRoutesKey.length]),
    menuCollapsed,
    setMenuCollapsed,
    setMenuOpenKeys,
    menuOpenKeys,
    menuNode,
    menuSelectedKey,
    menuOnOpenChange: useCallback(
      value => {
        routes.find(props => {
          if (props.name + props.path === value[value.length - 1]) {
            setMenuOpenKeys([value[value.length - 1]]);
            return true;
          } else {
            setMenuOpenKeys(value);
            return false;
          }
        });
      },
      [routes, setMenuOpenKeys],
    ),
  };
};
