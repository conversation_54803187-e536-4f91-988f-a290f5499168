import AppStore from '../../stores/AppStore';
import React, { useMemo, Suspense, useRef, useCallback, useState } from 'react';
import { RouteConfig } from '../../types';
import {
  matchPath,
  Redirect,
  Route,
  RouteProps,
  useLocation,
} from 'react-router-dom';
import { Spin } from 'antd';
import { KeepaliveRouterSwitch, KeepaliveRoute } from 'react-keepalive-router';

type RouteItem = RouteProps & { name: string; keep?: boolean };

const isMainApp = !window.__POWERED_BY_QIANKUN__;

export default (outRoutes?: RouteConfig[]) => {
  const { routes, permissions } = AppStore.useContainer();
  const homePathRef = useRef('');
  const [haveKeep, setHaveKeep] = useState(false);
  const filterFn = useCallback(
    (routes: RouteConfig[]) => {
      const routesArr: RouteItem[] = routes
        .map((config): RouteItem[] => {
          const {
            children = [],
            name,
            isDetail,
            inMenu = true,
            path,
            keep,
            ...props
          } = config;
          if (keep) {
            setHaveKeep(keep);
          }
          if (!permissions({ permissionsId: config.routeId, ...config }))
            return [];
          const isParent = children.some(({ isDetail = false }) => isDetail);
          if (
            inMenu &&
            !homePathRef.current &&
            path &&
            permissions({ permissionsId: config.routeId, ...config })
          ) {
            homePathRef.current = path + '';
          }
          const routeProps = {
            exact: !isParent,
            name,
            path,
            ...props,
          };
          return [routeProps, ...filterFn(children).routes];
        })
        .filter(arr => !!arr.length)
        .flat();
      return {
        routes: routesArr,
        homePath: homePathRef.current,
      };
    },
    [permissions],
  );
  const { routes: contentRoutes, homePath } = useMemo(
    () => filterFn(outRoutes || routes),
    [filterFn, outRoutes, routes],
  );
  const { pathname } = useLocation();
  return {
    homePath,
    contentRoutes: useMemo(() => {
      const isMatch = contentRoutes.some(
        ({ component, path, exact }) =>
          !!matchPath(pathname, { component, path, exact }),
      );
      const content = (
        <>
          {contentRoutes.map(props => {
            const RouteComponent = props.keep ? KeepaliveRoute : Route;
            return (
              <Suspense key={props.name + props.path} fallback={<Spin />}>
                <RouteComponent {...props} />
              </Suspense>
            );
          })}
          {isMainApp && !isMatch && <Redirect to={homePath} />}
        </>
      );
      return haveKeep ? (
        <KeepaliveRouterSwitch withoutRoute>{content}</KeepaliveRouterSwitch>
      ) : (
        content
      );
    }, [contentRoutes, haveKeep, homePath, pathname]),
  };
};
