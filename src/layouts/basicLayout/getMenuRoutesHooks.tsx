import { useEffect, useMemo, useState } from 'react';
import { RouteConfig } from '../../types';
import { matchPath } from 'react-router-dom';
import { SiteConfig } from '../../stores/SiteConfigStore';

export type GetRoutsOptions = Pick<
  SiteConfig,
  'routes' | 'routeIds' | 'permissions'
>;

const fn = ({
  routes,
  routeIds,
  permissions,
}: GetRoutsOptions): RouteConfig[] =>
  routes
    .map(
      (config): RouteConfig => {
        const { children } = config;
        const menuChildren = children
          ? fn({ routes: children, routeIds, permissions })
          : [];
        return {
          ...config,
          children: menuChildren.length ? menuChildren : undefined,
        };
      },
    )
    .filter(({ inMenu = true }) => inMenu)
    .filter(config => {
      const { children } = config;
      const someFn = (arr: typeof children): boolean =>
        !!arr &&
        arr.some(
          config =>
            permissions({ permissionsId: config.routeId, ...config }) ||
            someFn(config.children),
        );
      return (
        permissions({ permissionsId: config.routeId, ...config }) ||
        someFn(children)
      );
    })
    .filter(
      ({ children, component, menuRender }) =>
        !(!children && !component) || menuRender,
    );

export default (
  pathname: string,
  { routes, routeIds, permissions }: GetRoutsOptions,
) => {
  const [selectedKey, setSelectedKey] = useState(routes[0]?.name);
  useEffect(() => {
    const matchFn = (routes: RouteConfig[]): boolean => {
      const fined = routes.find(
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        props => !!matchPath(pathname, { exact: true, ...props }),
      );
      if (fined) {
        setSelectedKey(fined.name + fined.path);
      }
      return !!fined;
    };

    const flatRoutes: RouteConfig[] = [];
    const pushFlatRoute = (routes: RouteConfig[]) =>
      routes.forEach(props => {
        flatRoutes.push(props);
        if (props.children) {
          pushFlatRoute(props.children);
        }
      });
    pushFlatRoute(routes);

    matchFn(flatRoutes);
  }, [pathname, routes]);
  const resultRoutes = useMemo(() => fn({ routes, routeIds, permissions }), [
    permissions,
    routes,
    routeIds,
  ]);
  return {
    routes: resultRoutes,
    selectedKey,
  };
};
