import React from 'react';
import { Menu } from 'antd';
import { MenuProps as MProps } from 'antd/lib/menu';
import styled from 'styled-components';

export interface MenuProps extends Omit<MProps, 'selectedKey'> {
  selectedKey: string;
  children: React.ReactNode;
}

export default ({ selectedKey, ...props }: MenuProps) => {
  return (
    <MyMenu
      theme="dark"
      mode="inline"
      selectedKeys={[selectedKey]}
      {...props}
    />
  );
};

/**
 * 导出侧边菜单项，方便自定义菜单
 */
export { default as useGetMenuRoutes } from './getContentRoutesHooks';

const MyMenu = styled(Menu)`
  &&&& {
    padding: 16px 0;
  }
`;
