---
nav:
  title: 布局
---

# 基础布局

[Antd Pro](https://preview.pro.ant.design/dashboard/analysis) 经典的菜单栏加内容的布局。

```tsx
import React from 'react';
import { BasicLayout } from 'parsec-admin';
import DocuApp from '../../DocuApp';
import { HomeOutlined } from '@ant-design/icons';

export default () => (
  <DocuApp
    routes={[
      {
        name: 'hello',
        icon: <HomeOutlined />,
        children: [{
          name: 'hello2',
          path: '/layouts/basic-layout',
          component: () => <div>hello !!</div>,
        }]
      },
    ]}
    baseLayout={<BasicLayout />}
  />
);
```

## API

| 属性       | 说明                 | 默认值 |
| ---------- | -------------------- | ------ |
| header     | 自定义布局头部显示。 | -      |
| renderSide | 自定义侧边栏。       | -      |
| routes     | 自定义路由。         | -      |
| menuProps  | 侧边菜单 props。     | -      |
| sideProps  | 侧边栏 props。       | -      |
