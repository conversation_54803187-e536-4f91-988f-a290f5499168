import React, { useMemo } from 'react';
import { RouteConfig } from '../../types';
import { Menu } from 'antd';
import { Link } from 'react-router-dom';

const { SubMenu } = Menu;

const renderMenu = (routes: RouteConfig[]): React.ReactNode =>
  routes.map(config => {
    const { children, name, menuRender, icon, path } = config;
    const title = name;
    return children ? (
      <SubMenu key={name + path} title={title} icon={icon}>
        {renderMenu(children)}
      </SubMenu>
    ) : (
      <Menu.Item key={name + path} icon={icon}>
        {menuRender?.(config) || <Link to={path + ''}>{title}</Link>}
      </Menu.Item>
    );
  });

export default (routes: RouteConfig[]) =>
  useMemo(() => renderMenu(routes), [routes]);
