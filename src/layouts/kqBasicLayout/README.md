---
nav:
  title: 布局
---

## 凯桥基础布局

使用的时候需要在`app.tsx`引入`import 'parsec-admin/lib/layouts/kqBasicLayout/theme.less'`。

```tsx
import React from 'react';
import { KqBasicLayout } from 'parsec-admin';
import DocuApp from '../../DocuApp';
import { HomeOutlined } from '@ant-design/icons';
// import 'parsec-admin/lib/layouts/kqBasicLayout/theme.less';

export default () => {
  return (
    <DocuApp
      routes={[
        {
          name: 'hello',
          path: '/layouts/kq-basic-layout',
          component: () => <div>hello !!</div>,
          icon: <HomeOutlined/>,
          children: [{
            name: 'hello1',
            path: '/layouts/kq-basic-layout/aa',
            component: () => <div>hello !!</div>,
            icon: <HomeOutlined/>,
          }, {
            name: 'hello2',
            path: '/layouts/kq-basic-layout/aaa',
            component: () => <div>hello !!</div>,
            icon: <HomeOutlined/>,
          }]
        },
      ]}
      basicLayout={<KqBasicLayout header={<span style={{padding: '0 20px'}}>退出</span>}/>}
    />
  )
};
```

## API

| 属性      | 说明           | 默认值 |
| --------- | -------------- | ------ |
| sideProps | 侧边栏 props。 | -      |
