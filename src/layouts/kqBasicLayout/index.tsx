import React, { useMemo } from 'react';
import styled, { css } from 'styled-components';
import useCustomLayout from '../../layouts/basicLayout/customLayoutHooks';
import { Menu, Breadcrumb, Layout, Drawer, Card } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons';
import { BreadcrumbProps } from 'antd/lib/breadcrumb/Breadcrumb';
import { useHistory } from 'react-router';
import AppStore from '../../stores/AppStore';
import logoImg from './logo.png';
import menuBg from './menu-bg.png';
import sjSvg from './sj.svg';

const isMainApp = window.__POWERED_BY_QIANKUN__;

const { Sider } = Layout;

export const data = {
  used: false,
};

export default ({
  header,
  logo: logo2,
  topCls,
  autoTheme = true,
  asideCls,
  handleBack = () => Promise.resolve(),
}: {
  header?: React.ReactNode;
  topCls?: string;
  asideCls?: string;
  autoTheme?: boolean;
  logo?: string | ((collapsed: boolean) => React.ReactNode);
  handleBack?: () => Promise<any>;
}) => {
  data.used = true;
  const {
    menuNode,
    menuSelectedKey,
    isDetailPage,
    contentRoutes,
    breadcrumbs,
    itemRender,
    menuOpenKeys,
    menuOnOpenChange,
    menuCollapsed,
    isMobile,
    setMenuCollapsed,
    setDrawerVisible,
    drawerVisible,
    switchCollapsed,
  } = useCustomLayout();
  const history = useHistory();
  const {
    logo = logo2 instanceof Function ? logo2(menuCollapsed) : logo2 || logoImg,
  } = AppStore.useContainer();
  const aside = useMemo(
    () => (
      <Aside width={220} collapsed={menuCollapsed} className={asideCls}>
        <Logo collapsed={menuCollapsed}>
          {React.isValidElement(logo) ? logo : <img src={logo} alt="" />}
        </Logo>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[menuSelectedKey]}
          onOpenChange={menuOnOpenChange}
          openKeys={menuOpenKeys}
        >
          {menuNode}
        </Menu>
      </Aside>
    ),
    [
      asideCls,
      logo,
      menuCollapsed,
      menuNode,
      menuOnOpenChange,
      menuOpenKeys,
      menuSelectedKey,
    ],
  );
  return window.__POWERED_BY_QIANKUN__ && isMainApp ? (
    contentRoutes
  ) : (
    <BasicLayout>
      {isMobile ? (
        <MyDrawer
          width={220}
          placement={'left'}
          closable={false}
          onClose={() => {
            setDrawerVisible(!drawerVisible);
            switchCollapsed(!menuCollapsed);
          }}
          visible={drawerVisible}
        >
          {aside}
        </MyDrawer>
      ) : (
        aside
      )}
      <Content>
        <Header className={topCls}>
          <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <div
              style={{
                margin: isMobile ? '0 15px' : '0 30px 0 15px',
                fontSize: isMobile ? 20 : 30,
                cursor: 'pointer',
              }}
              onClick={() => {
                setMenuCollapsed(!menuCollapsed);
                setDrawerVisible(!drawerVisible);
              }}
            >
              {menuCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            </div>
            <MyBreadcrumb
              isMobile={isMobile}
              routes={breadcrumbs}
              itemRender={itemRender}
            />
            <div style={{ flex: 1 }} />
            {header}
          </div>
        </Header>
        {isDetailPage && (
          <Card
            style={{ margin: `24px 24px 0`, cursor: 'pointer' }}
            onClick={() => {
              handleBack().then(history.goBack);
            }}
          >
            <ArrowLeftOutlined />
            <span> 返回</span>
          </Card>
        )}
        <Main>{contentRoutes}</Main>
      </Content>
    </BasicLayout>
  );
};

const MyDrawer = styled(Drawer)`
  .ant-drawer-content {
    background: transparent;
  }
  .ant-drawer-body {
    padding: 0;
    height: 100vh;
  }
`;

const BasicLayout = styled.div`
  display: flex;
  background-color: #ecf3fa;
`;

const Logo = styled.div<{ collapsed: boolean }>`
  height: 80px;
  /* width: 100%; */
  background-color: #2e365b;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 155px;
  }
  h1 {
    color: #fff;
    margin: 0;
  }
  ${({ collapsed }) =>
    collapsed &&
    css`
      justify-content: end;
      overflow: hidden;
      img {
        margin-left: 13px;
      }
    `}
`;

const Aside = styled(Sider)`
  z-index: 2;
  min-height: 100vh;
  background-size:auto 100%;
  && {
    background:#2d355a url("${menuBg}") left bottom no-repeat;
  }
  .ant-menu-item-selected{
    position:relative;
    &:before{
      content:'';
      position:absolute;
      width:20px;
      height:20px;
      top:50%;
      right:-8px;
      transform:translateY(-50%);
      background:url("${sjSvg}") left bottom no-repeat;
    }
  }
`;

const MyBreadcrumb = styled(
  ({ isMobile, ...props }: BreadcrumbProps & { isMobile: boolean }) => (
    <Breadcrumb {...props} key={+new Date()} />
  ),
)`
  line-height: 1;
  a,
  .ant-breadcrumb-separator,
  .ant-breadcrumb-link {
    color: #fff !important;
    font-size: ${({ isMobile }) => (isMobile ? 14 : 20)}px;
  }
`;

const Header = styled.header`
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  background-image: linear-gradient(90deg, #2e365b 30%, #277ed5 100%);
`;
const Content = styled.section`
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 0;
`;

const Main = styled.main``;
