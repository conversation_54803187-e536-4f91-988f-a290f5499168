@import '~antd/dist/antd.less';

@primary-color: #2780da;
@info-color: #0ae;
@warn-color: #f57f17;
@danger-color: #f00;
@fail-color: @danger-color;
@title-color: #4c4c4c;
@border-color: #e1e8e7;
@body-color: #f2f8f8;
@border-radius-base: 4px;
@btn-border-radius-base: 8px;
@btn-border-radius-sm: 8px;
@btn-default-color: @primary-color;
@btn-default-border: @primary-color;
// @input-bg: #ecf3fa;
// @select-background: @input-bg;
@card-radius: 0;
@menu-item-height: 50px;
@menu-inline-toplevel-item-height: @menu-item-height;
@menu-bg: transparent;
@menu-dark-color: #fff;
@menu-dark-submenu-bg: @menu-bg;
@menu-dark-inline-submenu-bg: @menu-bg;
@menu-dark-bg: rgb(47,54,92);
@menu-dark-item-active-bg: #2d416e;
@menu-dark-selected-item-icon-color: #1890ff;
@menu-dark-selected-item-text-color: #1890ff;
@menu-icon-size: 16px;
@menu-icon-size-lg: 16px;
@menu-item-font-size: 16px;
@card-padding-base: 24px;
@padding-md: 20px;
// @pagination-item-size:
@table-border-radius-base: 0;
@table-body-sort-bg: #ecf3fa;
@table-selected-row-bg: #ecf3fa;
@background-color-light: #ecf3fa;
// @pagination-item-input-bg:#000;
.ant-menu-dark .ant-menu-item-selected > a {
  color: #1890ff;
  &:hover {
    color: #1890ff;
  }
}
.ant-table thead > tr > th {
  background: #ecf3fa;
}
.tableList-search-wrap {
  input,
  select,
  .ant-picker,
  .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
    background: #ecf3fa;
    border-radius: 8px;
  }
}

.ant-tabs-content {
  .ant-card {
    //margin: 0 !important;
  }
}
.ant-picker-clear {
  background-color: #ecf3fa;
}

.ant-descriptions-item-flex {
  > .ant-descriptions-item-content {
    width: 70%;
    vertical-align: text-top;
  }
  > .ant-descriptions-item-label {
    width: 100px;
  }
}
.ant-pagination-prev,
.ant-pagination-next {
  outline: 0;
  background: #2780d9;
  .anticon {
    color: #fff;
  }
}
.ant-pagination-item-active {
  font-weight: 500;
  background: #fff;
  border-color: #fff;
  &:focus {
    border-color: #fff;
  }
}
.ant-pagination-options-quick-jumper {
  input {
    background: #ecf3fa;
  }
}

.ant-pagination-item-link {
  background-color: transparent !important;
  border: none !important;
}

.page-login {
  .ant-input-affix-wrapper, .ant-input-affix-wrapper:hover {
    background-color: transparent !important;
    box-shadow: none !important;
  }
  input:-internal-autofill-selected {
    box-shadow: inset 0 0 0 500px transparent !important;
  }
}
