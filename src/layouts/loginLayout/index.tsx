import React from 'react';
import styled from 'styled-components';
import Footer from '../../components/footer';
import { Tabs, Spin } from 'antd';
import AppStore from '../../stores/AppStore';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import bg from './bg.svg';
import { getPrefixCls } from '../../_utils';

interface Props {
  children: React.ReactNode;
  loading: boolean;
}

export default ({ children, loading = false }: Props) => {
  const {
    logo,
    name = 'Ant Design',
    description = 'Ant Design 是西湖区最具影响力的 Web 设计规范',
  } = AppStore.useContainer();
  return (
    <Login style={{ backgroundImage: `url('${bg}')` }}>
      <Header>
        <div>
          {logo && <img src={logo} alt="" />}
          <span>{name}</span>
        </div>
        {description !== false ? <p>{description}</p> : <p />}
      </Header>
      <Container>
        <Spin spinning={loading}>
          <MyTabs animated={false}>{children}</MyTabs>
        </Spin>
      </Container>
      <Footer />
    </Login>
  );
};

const Login = styled.section`
  background-repeat: no-repeat;
  background-position: center 110px;
  background-size: 100%;
  width: 100%;
  min-height: 100vh;
  background-color: var(--login-loyout-background);
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 64px 0 24px;
  box-sizing: border-box;
  .${getPrefixCls}-spin-container::after {
    background: none;
  }
  @media (max-width: 770px) {
    background-image: none !important;
  }
`;
const Header = styled.header`
  display: flex;
  flex-direction: column;
  align-items: center;
  div {
    display: flex;
    align-items: center;
    padding: 0 16px;
    img {
      width: 44px;
      margin-right: 16px;
    }
    span {
      color: rgba(0, 0, 0, 0.85);
      font-size: 33px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      font-weight: 500;
    }
  }
  p {
    margin: 12px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 40px;
  }
`;
const Container = styled.div`
  flex: 1;
  padding: 32px 0;
  @media (min-width: 768px) {
    padding: 32px 0 24px;
  }
`;
const MyTabs = styled(Tabs)`
  max-width: 368px;
  width: 95vw;
  input {
    font-weight: 300;
  }
  > .${getPrefixCls}-tabs-nav {
    margin-left: 32px !important;
  }
  .${getPrefixCls}-tabs-bar.${getPrefixCls}-tabs-top-bar {
    border: none;
    margin-bottom: 24px;
  }
  .${getPrefixCls}-tabs-nav-scroll {
    display: flex;
    justify-content: center;
  }
  .${getPrefixCls}-tabs-nav .${getPrefixCls}-tabs-tab-active {
    font-weight: 400;
  }
  .${getPrefixCls}-tabs-nav-wrap {
    display: flex;
    justify-content: center;
  }
  .${getPrefixCls}-tabs-tab:last-of-type {
    margin-right: 0 !important;
  }
`;
