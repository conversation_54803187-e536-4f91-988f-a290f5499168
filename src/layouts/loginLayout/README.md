# 登录布局

基于 Antd Pro [用户登录页](https://preview.pro.ant.design/user/login)样式的布局。

```tsx
import React from 'react';
import { Form, Input, Tabs, Button } from 'antd';
import { LoginLayout } from 'parsec-admin';
import styled from 'styled-components';
import { TeamOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';

const { Password } = Input;
const { TabPane } = Tabs;

export default () => {
  return (
    <LoginLayout>
      <TabPane key={'1'} tab={'账号密码登录'}>
        <Form onFinish={console.log}>
          <FormItem
            name={'username'}
            rules={[
              {
                required: true,
                message: '请输入用户名',
              },
            ]}
          >
            <Input
              size={'large'}
              placeholder={'用户名'}
              prefix={<TeamOutlined />}
            />
          </FormItem>
          <FormItem
            name={'password'}
            rules={[
              {
                required: true,
                message: '请输入密码',
              },
            ]}
          >
            <Password
              size={'large'}
              placeholder={'密码'}
              type={'password'}
              prefix={<LockOutlined />}
            />
          </FormItem>
          <FormItem
            name={'captcha'}
            rules={[
              {
                required: true,
                message: '请输入验证码',
              },
            ]}
          >
            <Input
              size={'large'}
              placeholder={'验证码'}
              prefix={<MailOutlined />}
            />
          </FormItem>
          <FormItem>
            <Button
              size={'large'}
              block
              type={'primary'}
              htmlType={'submit'}
              style={{ fontWeight: 300 }}
            >
              登录
            </Button>
          </FormItem>
        </Form>
      </TabPane>
    </LoginLayout>
  );
};

const FormItem = styled(Form.Item)`
  .anticon {
    color: rgba(0, 0, 0, 0.25);
  }
`;
```

## API

| 属性    | 说明                   | 默认值  |
| ------- | ---------------------- | ------- |
| loading | 是否在提交或者加载中。 | `false` |
