/**
 * 基础布局
 */
export { default as BasicLayout } from './basicLayout';
/**
 * 凯桥基础布局
 */
export { default as KqBasicLayout } from './kqBasicLayout';
/**
 * 自定义布局需要用到的，会提供很多需要的东西
 * 参考 https://gitlab.parsec.com.cn/mushan/parsec-admin/-/blob/master/src/layouts/basicLayout/index.tsx
 */
export { default as useCustomLayout } from './basicLayout/customLayoutHooks';
export { default as Side } from './basicLayout/Side';
/**
 * 自定义的 Menu 请用这个
 */
export { Menu } from 'antd';
/**
 * 卡片布局
 */
export { default as CardLayout } from './cardLayout';
/**
 * 块布局
 */
export { default as BlankLayout } from './blankLayout';
export * from './blankLayout';
/**
 * 登录布局
 */
export { default as LoginLayout } from './loginLayout';
/**
 * 凯桥登录布局
 */
export { default as KqLoginLayout } from './kqLoginLayout';
/**
 * 详情布局
 * 样式来自于 https://preview.pro.ant.design/form/advanced-form
 */
export { default as DetailLayout } from './detailLayout';
export * from './detailLayout';
/**
 * 详情Tabs布局
 * 样式来自于 https://preview.pro.ant.design/user/login
 */
export { default as TabsDetailLayout } from './tabsDetailLayout';
export * from './tabsDetailLayout';
/**
 * 获取Menu改变事件
 */
export { default as useMenuChange } from './basicLayout/menuChangeHooks';
