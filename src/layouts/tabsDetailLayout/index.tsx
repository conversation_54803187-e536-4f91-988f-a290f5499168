import React from 'react';
import PageHeader, { MyPageHeaderProps } from '../../components/pageHeader';
import styled from 'styled-components';
import { Tabs } from 'antd';
import { TabsProps, TabPaneProps } from 'antd/lib/tabs';
import { getPrefixCls } from '../../_utils';
import { data as kqBasicLayoutData } from '../kqBasicLayout';
import useUrlState from '@ahooksjs/use-url-state';

const { TabPane } = Tabs;

export interface TabsDetailProps {
  headerProps?: MyPageHeaderProps | false;
  tabsProps?: TabsProps;
  tabs: (TabPaneProps & { children: React.ReactNode; key: string })[];
}

export default ({
  tabs,
  tabsProps = {},
  headerProps = kqBasicLayoutData.used ? false : undefined,
}: TabsDetailProps) => {
  const [{ tab }, setUrlState] = useUrlState(undefined, {
    navigateMode: 'replace',
  });
  const { onChange, defaultActiveKey = tab } = tabsProps;
  return (
    <>
      {headerProps && <PageHeader {...headerProps} />}
      <MyTabs
        {...tabsProps}
        defaultActiveKey={defaultActiveKey}
        onChange={tab => {
          setUrlState({ tab });
          onChange?.(tab);
        }}
      >
        {tabs.map(props => (
          <TabPane {...props} />
        ))}
      </MyTabs>
    </>
  );
};

const MyTabs = styled(Tabs)`
  .${getPrefixCls}-tabs {
    &-nav {
      background-color: var(--component-background);
      padding: 16px 24px 0;
      border: none;
      margin: ${() => (kqBasicLayoutData.used ? '24px' : '0')} !important;
      margin-bottom: -24px !important;
    }
  }
`;
