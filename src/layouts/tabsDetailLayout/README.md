# 标签页布局

基于 Antd Pro [高级详情页](https://preview.pro.ant.design/profile/advanced)样式的布局。

```tsx
/**
 * background: '#f6f7f9'
 */
import React from 'react';
import { TabsDetailLayout, BlankSection } from 'parsec-admin';

export default () => (
  <TabsDetailLayout
    tabs={[
      { key: '1', tab: '详情', children: <BlankSection>详情</BlankSection> },
      { key: '2', tab: '规则', children: <BlankSection>规则</BlankSection> },
    ]}
  />
);
```

## API

> 更多用法可以查看 [Tabs Props](https://ant.design/components/tabs-cn/#Tabs) 和 [BasicLayout Props](/layouts/blank-layout#api) 。

| 属性 | 说明                                                                          | 默认值 |
| ---- | ----------------------------------------------------------------------------- | ------ |
| tabs | 一组 [TabPhone Props](https://ant.design/components/tabs-cn/#Tabs.TabPane) 。 | -      |
