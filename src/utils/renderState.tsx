import React from 'react';
import { Badge } from 'antd';
import { OptionProps } from 'rc-select/lib/Option';

const stateArr = ['success', 'processing', 'default', 'error', 'warning'];

export type StateArr = typeof stateArr;

export default function<T>(data: T | OptionProps[], states?: StateArr) {
  return (state: keyof T) => {
    let text;
    let stateIndex;
    if (data instanceof Array) {
      data.forEach(({ value, children }, index) => {
        if (value === state) {
          text = children;
          stateIndex = index;
        }
      });
    } else {
      text = data[state];
      stateIndex = Object.keys(data).findIndex(
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        key => key + '' === state + '',
      );
    }
    return (
      <>
        {states && (
          <Badge
            status={text ? (states[stateIndex as any] as any) : 'default'}
          />
        )}
        {text || '暂无'}
      </>
    );
  };
}
