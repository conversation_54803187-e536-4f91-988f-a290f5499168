import { useEffect, useRef, useState } from 'react';

export default (ref: React.RefObject<HTMLElement>) => {
  const [isSticky, setIsSticky] = useState(false);
  const unmountRef = useRef(false);
  useEffect(() => {
    const fn = () => {
      if (ref.current) {
        const rect = ref.current.getClientRects()[0];
        setIsSticky(rect ? rect.top === 0 : false);
      }
      if (!unmountRef.current) {
        window.requestAnimationFrame(fn);
      }
    };
    fn();
    return () => {
      unmountRef.current = true;
    };
  }, [ref]);
  return isSticky;
};
