/* eslint-disable @typescript-eslint/ban-ts-comment */
import axios from 'axios';
import { notification } from 'antd';
import moment from 'moment';
import { appData } from '../app';
import { HttpLink } from 'apollo-link-http';
import { onError } from 'apollo-link-error';
import qs from 'qs';
import { render, unmountComponentAtNode } from 'react-dom';
import { Router } from 'react-router-dom';

const instance = axios.create();

const messageArr: string[] = [];
const showErrorMessage = (
  description: string,
  status: number,
  duration?: number,
) => {
  if (!messageArr.includes(description)) {
    messageArr.push(description);
    const {
      networkErrorNotification = () =>
        notification.error({
          description,
          message: `网络 ${status} 错误`,
          duration,
          onClose: () => {
            messageArr.splice(
              messageArr.findIndex(m => m === description),
              1,
            );
          },
        }),
    } = appData.storeValue;
    networkErrorNotification({ description, duration, status });
  }
};

const requestInterceptor = async (config: {
  params?: any;
  url?: string;
  headers?: any;
}) => {
  const {
    apiHost,
    specifiedToken = () => appData.storeValue.storage?.get('token'),
    requestInterceptor = config => config,
    authorizationHeader = true,
  } = appData.storeValue;
  if (config.url.includes('?')) {
    const [url, params] = config.url.split('?');
    config.params = { ...(config.params || {}), ...(qs.parse(params) || {}) };
    config.url = url;
  }
  Object.keys(config.params || {}).forEach(key => {
    const param = config.params[key];
    if (moment.isMoment(param)) {
      config.params[key] = param.toISOString();
    }
    if (param === '') {
      delete config.params[key];
    }
  });
  if (config.url) {
    if (!/http/.test(config.url) && apiHost) {
      const host = typeof apiHost === 'string' ? apiHost : apiHost(config.url);
      if (host !== '/') {
        config.url = host + config.url;
      }
    }
    const token = specifiedToken(config.url);
    if (token && authorizationHeader) {
      config.headers = {
        Authorization: `Bearer ${token}`.replace(/"/g, ''),
        ...config.headers,
      };
    }
  }
  config = await requestInterceptor(config);
  return config;
};

const responseFulfilledInterceptor = async (response: {
  headers: { token?: string };
  config: { url?: string };
}) => {
  const {
    headers: { token },
    config: { url } = { url: '' },
  } = response;
  const {
    storage,
    responseInterceptor = r => r,
    accordingUrlSetToken = () => storage?.set('token', token),
  } = appData.storeValue;
  if (token && url) {
    accordingUrlSetToken(url, token);
  }
  return Promise.resolve(await responseInterceptor(response as any));
};

const retryList = [];
let loginShow = false;

const responseRejectInterceptor = async (
  error: {
    response?: { data: any; status: number; config: { url: string } };
  } = {},
) => {
  const {
    response,
    response: {
      data: { message = '网络请求失败' } = {},
      status = 200,
      config,
      config: { url } = { url: '' },
    } = {},
  } = error;
  console.error(error, 'error');
  const {
    storage,
    errMsgDuration,
    handleLogonFailure = () => storage?.del('token'),
    responseInterceptor = r => r,
  } = appData.storeValue;
  let resolve, reject;
  const retry = (() =>
    new Promise((r, j) => {
      resolve = r;
      reject = j;
    }))();
  retryList.push(() => {
    // @ts-ignore
    delete config?.headers?.Authorization;
    // @ts-ignore
    delete config?.headers?.login_access_token;
    // @ts-ignore
    delete config?.params?.login_access_token;
    return instance(config)
      .then(resolve)
      .catch(reject);
  });
  if ([401, 403].includes(status)) {
    const {
      loginPath = '/login',
      tokenInvalidJump = () => history?.replace(loginPath),
      basename = '',
      history,
      aloneRoutes,
      routes,
      noJumpLogin,
    } = appData.storeValue;
    const replacePath = `${basename}${loginPath}`;
    const { component: Login } =
      aloneRoutes?.find(({ path }) => path === loginPath) || {};
    if (Login ? !loginShow : window.location.pathname !== replacePath) {
      showErrorMessage('登录过期，请重新登录', status, errMsgDuration);
      if (Login && noJumpLogin) {
        const dom = document.createElement('div');
        dom.style.position = 'fixed';
        dom.style.top = '0';
        dom.style.left = '0';
        dom.style.width = '100%';
        dom.style.height = '100%';
        dom.style.zIndex = '1000';
        document.body.appendChild(dom);
        loginShow = true;
        render(
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          <Router
            history={{
              ...history,
              replace(path) {
                if (path === '/' || path === routes[0].path) {
                  unmountComponentAtNode(dom);
                  document.body.removeChild(dom);
                  retryList.forEach(r => r());
                  loginShow = false;
                } else {
                  history.replace(replacePath);
                }
              },
            }}
          >
            {/*eslint-disable-next-line @typescript-eslint/ban-ts-comment*/}
            {/*@ts-ignore*/}
            <Login />
          </Router>,
          dom,
        );
      } else {
        handleLogonFailure(url);
        tokenInvalidJump(history);
      }
    } else {
      showErrorMessage('登录过期，请重新登录', status, errMsgDuration);
    }
  } else {
    showErrorMessage(message + '', status, errMsgDuration);
  }
  if (loginShow) {
    return retry;
  } else {
    resolve();
    return Promise.reject(await responseInterceptor(response as any));
  }
};

const httpLink = new HttpLink({
  fetch: async (input: RequestInfo, init: RequestInit = {}) => {
    if (input instanceof Request) {
      input = new Request({
        ...input,
        ...requestInterceptor({ ...input }),
      });
    } else {
      input = (await requestInterceptor({ url: input })).url || input;
    }
    if (init) {
      init = {
        ...init,
        ...(await requestInterceptor({ url: input as string, ...init })),
      };
    }
    return fetch(input, init).then(async response => {
      await responseFulfilledInterceptor({
        headers: { token: response.headers.get('token') || undefined },
        config: { url: response.url },
      });
      return response;
    });
  },
});

const baseLink = onError((error: any) => {
  console.error(error);
  const data =
    (error?.graphQLErrors || [])[0]?.extensions?.exception?.response ||
    error?.response?.errors[0] ||
    {};
  const { statusCode = 500, message } = data;
  responseRejectInterceptor({
    response: { status: statusCode, data: { message }, config: { url: '' } },
  });
  error.response = { errors: [], data };
  error.graphQLErrors = [];
});

export const apolloClientBaseLink = baseLink.concat(httpLink);

instance.interceptors.response.use(
  responseFulfilledInterceptor as any,
  responseRejectInterceptor,
);

instance.interceptors.request.use(requestInterceptor);

export default instance;
