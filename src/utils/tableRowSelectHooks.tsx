import React, { useMemo, useState } from 'react';
import { Button, Space } from 'antd';
import { ButtonProps } from 'antd/lib/button/button';
import actionConfirm from './actionConfirm';
import handleSubmit from './handleSubmit';

export default (
  buttonsProps?: (Omit<ButtonProps, 'onClick'> & {
    onClick: (ids: number[]) => Promise<any>;
    actionText: string;
    /**
     * @default true
     */
    confirm?: boolean;
  })[],
) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const paginationExtra = useMemo(
    () =>
      buttonsProps ? (
        <Space>
          {buttonsProps.map(({ actionText, confirm, ...props }) => (
            <Button
              disabled={!selectedRowKeys.length}
              children={actionText}
              {...props}
              onClick={() => {
                (confirm ? actionConfirm : handleSubmit)(
                  () => props.onClick(selectedRowKeys),
                  actionText,
                ).then(() => setSelectedRowKeys([]));
              }}
            />
          ))}
          <span> 已选择{selectedRowKeys.length}条</span>
        </Space>
      ) : (
        <div />
      ),
    [buttonsProps, selectedRowKeys],
  );
  return {
    selectedRowKeys,
    paginationExtra,
    setSelectedRowKeys,
    rowSelection: useMemo(
      () => ({
        onChange: (selectedRowKeys: any) => setSelectedRowKeys(selectedRowKeys),
        selectedRowKeys,
      }),
      [selectedRowKeys],
    ),
  };
};
