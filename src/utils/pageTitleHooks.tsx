import { useEffect } from 'react';
import RoutesTreeStore from '../stores/RoutesTreeStore';

const { title } = document;

export default (pageName: string) => {
  const { hideRoutesKey } = RoutesTreeStore.useContainer();
  useEffect(() => {
    if (pageName) {
      document.title = `${pageName} - ${title}`;
    } else {
      document.title = title;
    }
    return () => {
      document.title = title;
    };
  }, [pageName, hideRoutesKey]);
};
