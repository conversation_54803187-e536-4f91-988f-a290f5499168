import { message } from 'antd';
import { appData } from '../app';

export default (
  submitFn: () => Promise<any>,
  text: string | false = '保存',
  {
    tableName = 'all',
    resetForm,
  }: { tableName?: string; resetForm?: boolean } = {},
) => {
  return submitFn().then(() => {
    if (text !== false) {
      message.success(`${text}成功`);
    }
    appData.storeValue.reloadTableList(tableName, resetForm);
  });
};
