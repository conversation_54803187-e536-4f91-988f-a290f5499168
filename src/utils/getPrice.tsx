export default (num = 0, len = 2, split = false): string => {
  const float = num / 100;
  let result = (
    Math.round(float * Math.pow(10, len)) / Math.pow(10, len)
  ).toFixed(len);
  result = float !== parseFloat(result) ? float.toFixed(2).toString() : result;
  let splitResult = '';
  const number = (result.match(/\d+/) || [''])[0];
  [...number]
    .reverse()
    .forEach(
      (item, index) =>
        (splitResult += `${item}${
          !((index + 1) % 3) && index + 1 !== number.length ? ',' : ''
        }`),
    );
  return split
    ? result.replace(number, [...splitResult].reverse().join(''))
    : result;
};
