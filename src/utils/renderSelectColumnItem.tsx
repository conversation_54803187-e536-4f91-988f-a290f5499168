import React from 'react';
import { ArrSelect } from '../components';
import renderState, { StateArr } from './renderState';
import { OptionProps } from 'rc-select/lib/Option';

export default function <T>(data: T | OptionProps[], status?: StateArr) {
  return {
    search: (
      <ArrSelect
        allowClear
        options={
          data instanceof Array
            ? data
            : Object.keys(data).map((key) => ({
                value: /^[0-9]+$/.test(key) ? +key : key,
                children: (data as any)[key],
              }))
        }
      />
    ),
    render: renderState(data, status),
  };
}
