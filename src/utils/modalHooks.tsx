import React, { useCallback, useRef, useState } from 'react';
import { useRefState, usePortal } from 'parsec-hooks';
import { FormInstance } from 'antd/lib/form/hooks/useForm';
import { Form } from 'antd';
import FormModal, {
  FormModalProps as FormModalProps2,
} from '../components/formModal';

export interface FormModalProps<D> extends FormModalProps2<D> {
  subTitle?: React.ReactNode;
}

function useModal<D = any>(
  props:
    | ((
        values: D & { switchVisible: (props?: FormModalProps<D>) => void },
        form: FormInstance,
      ) => FormModalProps<D>)
    | FormModalProps<D>,
  /**
   * 不传的话就不会重新渲染
   */
  deps?: React.DependencyList,
): (values?: D) => void;

function useModal<D>(props, deps = []) {
  const [defaultForm] = Form.useForm();
  const [values, setValues] = useState({} as D);
  const [submitLoading, setSubmitLoading] = useRefState(false);
  const formRef = useRef(defaultForm);
  const formInitRef = useRef(false);
  const switchModalVisible = usePortal<FormModalProps<D>>(
    useCallback(
      ({ visible, switchVisible }) => {
        const { form = defaultForm, onSubmit, title, subTitle, ...modalProps } =
          props instanceof Function
            ? props({ ...values, switchVisible }, defaultForm)
            : props;
        if (!formInitRef.current) {
          const oldSet = form.setFieldsValue;
          form.setFieldsValue = (newValues: D) => {
            if (JSON.stringify(newValues) !== '{}') {
              setValues(values => ({ ...values, ...newValues }));
            }
            oldSet(newValues);
          };
          formInitRef.current = true;
        }
        formRef.current = form;
        return (
          <FormModal
            visible={visible}
            form={form}
            confirmLoading={submitLoading}
            okText="确定"
            okButtonProps={{
              className: visible ? 'modalOk' : undefined,
            }}
            cancelText="取消"
            {...modalProps}
            myFormProps={{
              ...modalProps.myFormProps,
              data: {
                ...modalProps.myFormProps?.formProps?.initialValues,
                ...modalProps.myFormProps?.initialValues,
                ...values,
                ...modalProps.myFormProps?.data,
              },
              formProps: {
                ...modalProps.myFormProps?.formProps,
                onValuesChange: (...arg) => {
                  if (props instanceof Function) {
                    const newValue = arg[1] as D;
                    Object.keys(newValue).forEach(key => {
                      if (newValue[key] === undefined) {
                        delete newValue[key];
                      }
                    });
                    setValues(values => ({ ...values, ...newValue }));
                  }
                  modalProps.myFormProps?.formProps?.onValuesChange?.(...arg);
                },
              },
            }}
            onSubmit={async () => {
              if (onSubmit) {
                await form
                  .validateFields()
                  .then((values: D) => {
                    setSubmitLoading(true);
                    return onSubmit(values);
                  })
                  .finally(() => setSubmitLoading(false));
              }
              switchVisible({ visible: false });
              setSubmitLoading(false);
            }}
            onCancel={e => {
              switchVisible({ visible: false });
              modalProps.onCancel?.(e);
            }}
            title={
              title && (
                <div style={{ display: 'flex', alignItems: 'baseline' }}>
                  {title}{' '}
                  {subTitle && (
                    <span
                      style={{
                        fontSize: 14,
                        marginLeft: 10,
                        color: 'rgba(0, 0, 0, 0.46)',
                        fontWeight: 400,
                      }}
                    >
                      {subTitle}
                    </span>
                  )}
                </div>
              )
            }
          />
        );
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [defaultForm, setSubmitLoading, submitLoading, values, ...deps],
    ),
  );
  return useCallback(
    (values = {} as D) => {
      setValues(values);
      formRef.current.setFieldsValue(values);
      switchModalVisible();
    },
    [switchModalVisible],
  );
}

export default useModal;
