import { message } from 'antd';

const messageArr: string[] = [];

const obj = {
  ...message,
};

['info', 'success', 'error', 'warning', 'loading'].forEach(key => {
  obj[key] = (...arg) => {
    const isObjArg = typeof arg[0] !== 'string';
    const msg = isObjArg ? arg[0].message : arg[0];
    if (isObjArg) {
      const oldFN = arg[0].onClose;
      arg[0].onClose = () => {
        oldFN?.();
        messageArr.splice(messageArr.indexOf(msg), 1);
      };
    } else {
      const oldFN = arg[2];
      arg[2] = () => {
        oldFN?.();
        messageArr.splice(messageArr.indexOf(msg), 1);
      };
    }
    if (!messageArr.includes(msg)) {
      messageArr.push(msg);
      const fn = message[key];
      return fn(...arg);
    }
  };
});

export default obj;
