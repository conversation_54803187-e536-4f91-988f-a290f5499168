export default (record: any, value) => {
  // 判断密码连续重复字符是否超过3个
  const repeatString = (password: string) => {
    const chars = Array.from(password);
    let repeat = 0;
    let tmpC: string = chars[0];
    for (let i = 1; i < chars.length; i++) {
      if (tmpC === chars[i]) {
        repeat++;
      } else {
        tmpC = chars[i];
        repeat = 0;
      }
      if (repeat >= 2) {
        return true;
      }
    }
    return false;
  };
  if (value) {
    const pwdRegx = new RegExp(
      '(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[^a-zA-Z0-9]).{8,30}',
    );
    if (!pwdRegx.test(value)) {
      return Promise.reject(
        new Error(
          `您的密码复杂度太低(密码中必须包含大小写字母、数字、特殊字符)且长度须大于8位，请及时修改密码!`,
        ),
      );
    } else {
      if (repeatString(value)) {
        return Promise.reject(new Error(`您的密码连续重复字符数超过了3个`));
      }
      return Promise.resolve();
    }
  } else {
    return Promise.reject(new Error('请输入密码'));
  }
};
