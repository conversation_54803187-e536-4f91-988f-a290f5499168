import { RouteComponentProps, useParams } from 'react-router-dom';
import React, { useEffect } from 'react';
import RoutesTreeStore from '../stores/RoutesTreeStore';
import { RouteConfig } from '../types';
import usePageTitle from './pageTitleHooks';

const eachRoutes = (routes: RouteConfig[], parentKey = '') =>
  routes.forEach(config => {
    const {
      microAppName,
      component: Component = microAppName
        ? () => {
            useEffect(() => {
              if (document.getElementById(microAppName)) return;
              const content =
                document.getElementById(microAppName) ||
                document.createElement('div');
              content.id = microAppName;
              document
                .getElementById('micro-app-placeholder')
                .appendChild(content);
            }, []);
            const containerId = 'micro-app-container';
            useEffect(() => {
              const microAppContainer = document.getElementById(containerId);
              const target = document.getElementById('micro-app-placeholder');
              if (microAppContainer && target) {
                [...target.childNodes, ...microAppContainer.childNodes].forEach(
                  (item: any) => {
                    if (item.id === microAppName) {
                      microAppContainer.insertBefore(item, null);
                    }
                  },
                );
              }
              return () => {
                if (microAppContainer && target) {
                  [
                    ...target.childNodes,
                    ...microAppContainer.childNodes,
                  ].forEach((item: any) => {
                    if (item.id === microAppName) {
                      target.insertBefore(item, null);
                    }
                  });
                }
              };
            }, []);
            return <div id={containerId} />;
          }
        : undefined,
      name,
      isDetail = false,
      children = [],
      exact,
    } = config;
    const isParent = children.some?.(({ isDetail }) => !!isDetail);
    if (microAppName) {
      config.exact = false;
    }
    if (isParent && exact === undefined) {
      config.exact = !isParent;
    }
    if (Component) {
      config.component = (props: RouteComponentProps) => (
        <Children
          isMicroApp={!!microAppName}
          parentKey={parentKey}
          isDetail={isDetail}
          currentKey={name}
          childrenKeys={children.map(({ name }) => name)}
        >
          <Component {...props} />
        </Children>
      );
    }
    if (children.length) {
      eachRoutes(children, name);
    }
  });

export default eachRoutes;

interface ChildrenProps {
  isDetail: boolean;
  parentKey: string;
  currentKey: string;
  children: React.ReactElement;
  childrenKeys: string[];
  isMicroApp: boolean;
}

const Children = ({
  isDetail,
  parentKey,
  children,
  currentKey,
  childrenKeys,
  isMicroApp,
}: ChildrenProps) => {
  const {
    hideRoutesKey,
    showRoute,
    hideRoute,
    reloadList,
  } = RoutesTreeStore.useContainer();
  useEffect(() => {
    if (isDetail && parentKey) {
      hideRoute(parentKey);
    }
    return () => {
      showRoute?.(parentKey);
    };
  }, [parentKey, isDetail, showRoute, hideRoute, currentKey]);
  usePageTitle(isMicroApp ? '' : currentKey);
  return (
    <div
      style={{
        display:
          (hideRoutesKey?.includes(currentKey) ||
            childrenKeys.some(key => hideRoutesKey?.includes(key))) &&
          !isMicroApp
            ? 'none'
            : '',
      }}
    >
      {React.cloneElement(children, { reloadTableList: reloadList })}
    </div>
  );
};
