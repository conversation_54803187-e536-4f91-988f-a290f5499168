---
nav:
  title: 工具
---

# 操作确认

通常用在表格操作

```tsx
import React, { useMemo } from 'react';
import { Button, Space, Form, Radio, Input } from 'antd';
import { PortalProvider, actionConfirm } from 'parsec-admin';

const Demo = () => {
  return (
    <Space>
      <Button
        type={'primary'}
        onClick={() => actionConfirm(async () => {
          // await 删除接口
        }, '删除').then(() => console.log('确认')).catch(() => console.log('取消'))}
      >
        删除
      </Button>
    </Space>
  );
};

export default () => (
  <PortalProvider>
    <Demo />
  </PortalProvider>
);
```
