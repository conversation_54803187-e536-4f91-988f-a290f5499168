import { message, Modal } from 'antd';
import { ModalFuncProps } from 'antd/lib/modal';
import { appData } from '../../app';

const { confirm } = Modal;

export default (
  fn: () => Promise<any>,
  text: string,
  {
    reloadList = true,
    template = '确定要{{text}}吗？',
    showMessage = true,
    props,
  }: {
    reloadList?: boolean;
    template?: string | false;
    showMessage?: boolean;
    props?: ModalFuncProps;
  } = {},
) => {
  const {
    prefixCls = 'ant',
    reloadTableList,
    confirmTemplate = template,
  } = appData.storeValue;
  Modal.config({
    rootPrefixCls: prefixCls,
  });
  return new Promise((resolve, reject) => {
    const modal = confirm({
      prefixCls: `${prefixCls}-modal`,
      title:
        confirmTemplate && template !== false
          ? confirmTemplate.replace('{{text}}', text)
          : text,
      okText: '确定',
      cancelText: '取消',
      onOk: () =>
        fn().then(e => {
          resolve(e);
          if (reloadList) {
            reloadTableList?.('all');
          }
          if (showMessage) {
            message.success(
              `${confirmTemplate && template !== false ? text : '操作'}成功`,
            );
          }
        }),
      ...props,
      onCancel: e => {
        modal.destroy();
        reject(e);
        props?.onCancel?.(e);
      },
    });
  });
};
