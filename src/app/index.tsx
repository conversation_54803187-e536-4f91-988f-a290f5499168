import React, { useEffect, useMemo } from 'react';
import {
  Route,
  matchPath,
  Redirect,
  useLocation,
  Router,
} from 'react-router-dom';
import RoutesTreeStore from '../stores/RoutesTreeStore';
import RequestConfigStore from '../stores/RequestConfigStore';
import AppStore, { AppStoreValue } from '../stores/AppStore';
import UploadConfigStore from '../stores/UploadConfigStore';
import StyledConfigStore from '../stores/StyledConfigStore';
import wrapRoutes from '../utils/wrapRoutes';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { createBrowserHistory } from 'history';
import { PortalProvider } from 'parsec-hooks';
import { registerMicroApps, start } from 'qiankun';

import './App.less';

moment.locale('zh-cn');

export const appData: { storeValue: AppStoreValue } = { storeValue: {} as any };

const App = () => {
  const {
    storage,
    aloneRoutes,
    basicLayout,
    loginPath = '/login',
    checkLogin = () => !!storage?.get('token'),
    routes,
  } = AppStore.useContainer();
  const { pathname } = useLocation();
  const validLogin = checkLogin(pathname);
  const { reloadList } = RoutesTreeStore.useContainer();
  appData.storeValue = { ...appData.storeValue, reloadTableList: reloadList };
  const matchAloneRoutes = aloneRoutes.some(
    props => !!matchPath(pathname, props),
  );
  useEffect(() => {
    const names = [];
    const configs = [...routes, ...aloneRoutes].filter(({ microAppName }) => {
      const is = !names.includes(microAppName) && microAppName;
      names.push(microAppName);
      return is;
    });
    registerMicroApps(
      configs.map(({ microAppUrl, microAppName, path, microAppProps }) => {
        if (!document.getElementById(microAppName)) {
          const content =
            document.getElementById(microAppName) ||
            document.createElement('div');
          content.id = microAppName;
          document.getElementById('micro-app-placeholder').appendChild(content);
        }
        return {
          name: microAppName,
          entry: microAppUrl,
          container: `#${microAppName}`,
          activeRule: `/`,
          props: microAppProps,
        };
      }),
    );

    start({
      sandbox: false,
      singular: false,
    });
  }, [aloneRoutes, matchAloneRoutes, routes]);

  return (
    <>
      <Route path={'/'}>
        {!matchAloneRoutes &&
          (validLogin
            ? basicLayout
            : pathname !== loginPath && <Redirect to={loginPath} />)}
      </Route>
      {(validLogin || matchAloneRoutes
        ? aloneRoutes
        : aloneRoutes.filter(({ path }) => path === loginPath)
      ).map(props => (
        <AloneRouteWrap key={props.path + ''}>
          <Route {...props} />
        </AloneRouteWrap>
      ))}
    </>
  );
};

const AloneRouteWrap = ({ children }: { children: React.ReactElement }) => {
  const { reloadList } = RoutesTreeStore.useContainer();
  return React.cloneElement(children, {
    reloadTableList: reloadList,
  });
};

export default ({ ...appValue }: Omit<AppStoreValue, 'reloadTableList'>) => {
  wrapRoutes(appValue.routes);
  wrapRoutes(appValue.aloneRoutes as any);
  const { basename, prefixCls } = appValue;
  appValue.history = useMemo(
    () =>
      appValue.history ||
      createBrowserHistory({
        basename,
      }),
    [appValue.history, basename],
  );
  const routeIds = appValue.routeIds;
  appValue.permissions = useMemo(
    () =>
      appValue.permissions ||
      (({ permissionsId }) => {
        if (routeIds) {
          if (permissionsId instanceof Array) {
            let flag = false;
            (permissionsId || []).map(x => {
              if (routeIds.includes(x)) {
                flag = true;
              }
            });
            return flag;
          } else {
            return !permissionsId || routeIds.includes(permissionsId);
          }
        } else {
          return true;
        }
      }),
    [appValue.permissions, routeIds],
  );
  const storeValue = appValue as AppStoreValue;
  appData.storeValue = storeValue;
  return (
    <RequestConfigStore.Provider initialState={storeValue}>
      <UploadConfigStore.Provider initialState={storeValue}>
        <AppStore.Provider initialState={storeValue}>
          <StyledConfigStore prefixCls={prefixCls}>
            {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
            {/*@ts-ignore*/}
            <Router history={appValue.history}>
              <RoutesTreeStore.Provider>
                <PortalProvider>
                  <App />
                  <div
                    id={'micro-app-placeholder'}
                    style={{ display: 'none' }}
                  />
                </PortalProvider>
              </RoutesTreeStore.Provider>
            </Router>
          </StyledConfigStore>
        </AppStore.Provider>
      </UploadConfigStore.Provider>
    </RequestConfigStore.Provider>
  );
};
