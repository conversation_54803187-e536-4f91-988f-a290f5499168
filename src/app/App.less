@ant-prefix: ant;

body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, segoe ui, Roboto,
    helvetica neue, Arial, noto sans, sans-serif, apple color emoji,
    segoe ui emoji, segoe ui symbol, noto color emoji;
  font-feature-settings: 'tnum';
  -webkit-font-smoothing: antialiased;
}

.@{ant-prefix}-table-content {
  .@{ant-prefix}-table-thead {
    position: sticky;
    top: 0;
    z-index: 1;
  }
  //overflow: hidden !important;
  //.@{ant-prefix}-table-content {
  //  overflow: hidden !important;
  //}
  //&:hover {
  //  overflow: auto !important;
  //  .@{ant-prefix}-table-content {
  //    overflow: auto !important;
  //  }
  //}
}

.anticon {
  &::before {
    display: none !important;
  }
}

@component-background: #fff;

html {
  --component-background: @component-background;
  --login-loyout-background: #f0f2f5;
}

