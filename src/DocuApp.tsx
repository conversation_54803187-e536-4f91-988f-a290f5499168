import React from 'react';
import App, { BasicLayout } from '.';
import { AppStoreValue } from './stores/AppStore';

type keys = 'token';

interface Storage {
  get: {
    (key: keys): string | null;
  };
  set: {
    (key: keys, value: string): void;
  };
  del: {
    (key: keys): void;
  };
  clear: () => void;
}

const prefix = 'parsec-app';

const storage: Storage = {
  get: key => sessionStorage.getItem(`${prefix}-${key}`),
  set: (key, value) =>
    value && sessionStorage.setItem(`${prefix}-${key}`, value),
  del: key => sessionStorage.removeItem(`${prefix}-${key}`),
  clear: () => sessionStorage.clear(),
};

export default (props: Partial<AppStoreValue>) => (
  <App
    apiHost={''}
    storage={storage}
    routes={[]}
    aloneRoutes={[]}
    basicLayout={<BasicLayout />}
    checkLogin={() => true}
    uploadFn={async file => file as any}
    {...props}
  />
);
